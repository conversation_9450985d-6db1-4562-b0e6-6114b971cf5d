package handler

import (
	"github.com/google/wire"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_tag"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/abtesting"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_stock"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/order"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/seller_tag"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/shop"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/redishelper"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/application/service"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/address"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_grouping"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/shop_local_sip"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/soc"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/warehouse_priority"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/external/spexlib"
)

var ProviderSet = wire.NewSet(
	wire.Struct(new(RestServer), "*"),
	redishelper.InitRedisClientMap,
	NewItemGroupingHandler,
	service.ItemGroupingServiceProviderSet,
	item_grouping.ItemGrouperProviderSet,
	item_grouping.ShippingOrderProcessorProviderSet,
	item_grouping.ItemOrganizerProviderSet,
	shop_local_sip.ShopLocalSIPServiceProviderSet,
	address.AddressServiceProviderSet,
	soc.SOCServiceProviderSet,
	warehouse_priority.WarehousePriorityServiceProviderSet,
	spexlib.SpexProviderSet,
	item_stock.ItemServiceProviderSet,
	item_stock.ItemStockServiceProviderSet,
	seller_tag.SellerTagServiceProviderSet,
	seller_tag.SellerTagApiProviderSet,
	abtesting.AbtestServiceProvideSet,
	order.SalesOrdersCountServiceProvideSet,
	order.CacheStoreProvideSet,
	shop.ShopServiceProviderSet,
	item_tag.ItemTagApiProviderSet,
	item_tag.ItemTagServiceProviderSet,
)
