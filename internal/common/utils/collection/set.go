package collection

type Set[T any] interface {
	Size() int

	Add(value T)
	Remove(value T)
	Contains(value T) bool

	Range(func(item T) (stopped bool))
	Values() []T

	Union(other Set[T]) Set[T]
	Intersection(other Set[T]) Set[T]
	Difference(other Set[T]) Set[T]
	ToSlice() []T
}

func NewSet[T comparable]() Set[T] {
	return &baseSet[T]{data: make(map[T]struct{})}
}

type baseSet[T comparable] struct {
	data map[T]struct{}
}

func (s *baseSet[T]) Size() int {
	return len(s.data)
}

func (s *baseSet[T]) Add(value T) {
	s.data[value] = struct{}{}
}

func (s *baseSet[T]) Remove(value T) {
	delete(s.data, value)
}

func (s *baseSet[T]) Contains(value T) bool {
	_, ok := s.data[value]
	return ok
}

func (s *baseSet[T]) Values() []T {
	result := make([]T, 0, len(s.data))
	for item := range s.data {
		result = append(result, item)
	}
	return result
}

func (s *baseSet[T]) Range(f func(item T) (stopped bool)) {
	for item := range s.data {
		if !f(item) {
			break
		}
	}
}

func (s *baseSet[T]) Union(other Set[T]) Set[T] {
	result := NewSet[T]()
	s.Range(func(item T) bool {
		result.Add(item)
		return false
	})
	other.Range(func(item T) bool {
		result.Add(item)
		return false
	})
	return result
}

func (s *baseSet[T]) Intersection(other Set[T]) Set[T] {
	result := NewSet[T]()
	s.Range(func(item T) bool {
		if other.Contains(item) {
			result.Add(item)
		}
		return false
	})
	return result
}

func (s *baseSet[T]) Difference(other Set[T]) Set[T] {
	result := NewSet[T]()
	s.Range(func(item T) bool {
		if !other.Contains(item) {
			result.Add(item)
		}
		return false
	})
	return result
}

func (s *baseSet[T]) ToSlice() []T {
	if s == nil {
		slice := make([]T, 0)
		return slice
	}
	slice := make([]T, 0, len(s.data))
	for k := range s.data {
		slice = append(slice, k)
	}
	return slice
}
