package business_config

import (
	"context"

	abconfig "git.garena.com/shopee/experiment-platform/abtest-core/v2/api/config"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/utils"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
)

type BusinessAccessor interface {
	GetMigrationConfig(ctx context.Context) MigrationConfig
	GetIGS3PFShopFlags(ctx context.Context) []uint64
	GetIGSLFFShopFlags(ctx context.Context) []uint64
	GetLFFWarehouseRegionPriority(ctx context.Context) []string
	GetIGSDynamicConfig(ctx context.Context) IGSDynamicConfig
	IsEnableWarehouseRegionForAddressAPI(ctx context.Context) bool
	GetDefaultSIPPrimaryRegionToDummyBuyerGeoLocationConfig(ctx context.Context) map[string]GeoLocationConfig
	GetDefaultBuyerGeoLocationConfig(ctx context.Context) typ.Optional[GeoLocationConfig]
	GetABTestingConfig(ctx context.Context) abconfig.Config
	GetEnableCachedSellerTagFlow(ctx context.Context) bool
	GetBatchGetEntityTagAPISize(ctx context.Context) int
	GetAllocateWarehouseBySalesOrderGroups(ctx context.Context) []AllocateWarehouseBySalesOrdersGroup
	GetSupportSellerMultiWhPFF(ctx context.Context) bool
	GetSellerMultiWHWithPartialFBSConfig(ctx context.Context) SellerMultiWhWithPartialFBSConfig
	GetEnableAllocateWarehouseBySalesOrders(ctx context.Context) bool
	GetEnable3PFIgnoreSellerTag(ctx context.Context) bool

	Init(ctx context.Context) error
}

func NewBusinessAccessorImpl() *BusinessAccessorImpl {
	confListener := utils.NewConfListener(businessPrefix, refreshConfigInterval, false)
	accessor := &BusinessAccessorImpl{
		ConfListener: confListener,
	}
	return accessor
}

type BusinessAccessorImpl struct {
	ConfListener utils.ConfListener
}

func (a *BusinessAccessorImpl) GetMigrationConfig(ctx context.Context) MigrationConfig {
	return migrationConfig.ValueCtx(ctx).Get()
}

func (a *BusinessAccessorImpl) GetIGS3PFShopFlags(ctx context.Context) []uint64 {
	return igs3PFShopFlagsConfig.ValueCtx(ctx).Get()
}

func (a *BusinessAccessorImpl) GetIGSLFFShopFlags(ctx context.Context) []uint64 {
	return igsLFFShopFlags.ValueCtx(ctx).Get()
}

func (a *BusinessAccessorImpl) GetLFFWarehouseRegionPriority(ctx context.Context) []string {
	return lffWarehouseRegionPriority.ValueCtx(ctx).Get()
}

func (a *BusinessAccessorImpl) GetIGSDynamicConfig(ctx context.Context) IGSDynamicConfig {
	return igsDynamicConfig.ValueCtx(ctx).Get()
}

func (a *BusinessAccessorImpl) IsEnableWarehouseRegionForAddressAPI(ctx context.Context) bool {
	return enableWarehouseRegionForAddressAPIConfig.ValueCtx(ctx).Get()
}

func (a *BusinessAccessorImpl) GetDefaultSIPPrimaryRegionToDummyBuyerGeoLocationConfig(ctx context.Context) map[string]GeoLocationConfig {
	return defaultSIPPrimaryRegionToDummyBuyerGeoLocationConfig.ValueCtx(ctx).Get()
}

func (a *BusinessAccessorImpl) GetDefaultBuyerGeoLocationConfig(ctx context.Context) typ.Optional[GeoLocationConfig] {
	return defaultBuyerGeoLocationConfig.ValueCtx(ctx)
}

func (a *BusinessAccessorImpl) GetABTestingConfig(ctx context.Context) abconfig.Config {
	return abTestingConfig.ValueCtx(ctx).Get()
}

func (a *BusinessAccessorImpl) GetEnableCachedSellerTagFlow(ctx context.Context) bool {
	return enableCachedSellerTagFlowConfig.ValueCtx(ctx).Get()
}

func (a *BusinessAccessorImpl) GetBatchGetEntityTagAPISize(ctx context.Context) int {
	return batchGetEntityTagAPISizeConfig.ValueCtx(ctx).Get()
}

func (a *BusinessAccessorImpl) GetAllocateWarehouseBySalesOrderGroups(ctx context.Context) []AllocateWarehouseBySalesOrdersGroup {
	return allocateWarehouseBySalesOrdersGroupConfig.ValueCtx(ctx).Get()
}

func (a *BusinessAccessorImpl) GetSupportSellerMultiWhPFF(ctx context.Context) bool {
	return supportSellerMultiWhPFFConfig.ValueCtx(ctx).Get()
}

func (a *BusinessAccessorImpl) GetSellerMultiWHWithPartialFBSConfig(ctx context.Context) SellerMultiWhWithPartialFBSConfig {
	return sellerMultiWhWithPartialFBSConfig.ValueCtx(ctx).Get()
}

func (a *BusinessAccessorImpl) GetEnableAllocateWarehouseBySalesOrders(ctx context.Context) bool {
	return enableAllocateWarehouseBySalesOrders.ValueCtx(ctx).Get()
}

func (a *BusinessAccessorImpl) GetEnable3PFIgnoreSellerTag(ctx context.Context) bool {
	return enable3PFIgnoreSellerTagConfig.ValueCtx(ctx).Get()
}

func (a *BusinessAccessorImpl) Init(ctx context.Context) error {
	a.ConfListener.Register(migrationConfig)
	a.ConfListener.Register(igs3PFShopFlagsConfig)
	a.ConfListener.Register(igsLFFShopFlags)
	a.ConfListener.Register(lffWarehouseRegionPriority)
	a.ConfListener.Register(igsDynamicConfig)
	a.ConfListener.Register(enableWarehouseRegionForAddressAPIConfig)
	a.ConfListener.Register(defaultSIPPrimaryRegionToDummyBuyerGeoLocationConfig)
	a.ConfListener.Register(defaultBuyerGeoLocationConfig)
	a.ConfListener.Register(abTestingConfig)
	a.ConfListener.Register(enableCachedSellerTagFlowConfig)
	a.ConfListener.Register(batchGetEntityTagAPISizeConfig)
	a.ConfListener.Register(allocateWarehouseBySalesOrdersGroupConfig)
	a.ConfListener.Register(supportSellerMultiWhPFFConfig)
	a.ConfListener.Register(sellerMultiWhWithPartialFBSConfig)
	a.ConfListener.Register(enableAllocateWarehouseBySalesOrders)
	a.ConfListener.Register(enable3PFIgnoreSellerTagConfig)
	return a.ConfListener.Init(ctx)
}
