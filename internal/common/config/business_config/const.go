package business_config

import "time"

const (
	refreshConfigInterval = 10 * time.Minute
)

const (
	businessPrefix = "business"

	MigrationKey                                      = "migration"
	IGS3PFShopFlagsKey                                = "igs_3pf_shop_flags"
	IGSLFFShopFlagsKey                                = "igs_lff_shop_flags"
	LFFWarehouseRegionPriorityKey                     = "lff_warehouse_region_priority"
	IGSDynamicConfigKey                               = "igs_dynamic_config"
	EnableWarehouseRegionForAddressAPIKey             = "enable_warehouse_region_for_address_api"
	DefaultSIPPrimaryRegionToDummyBuyerGeoLocationKey = "default_sip_primary_region_to_dummy_buyer_geo_location"
	DefaultBuyerGeoLocationKey                        = "default_buyer_geo_location"
	AbTestingKey                                      = "ab_testing"
	Enable3PFIgnoreSellerTagKey                       = "enable_3pf_ignore_seller_tag"
	EnableCachedSellerTagFlowKey                      = "enable_cached_seller_tag_flow"
	BatchGetEntityTagAPISizeKey                       = "batch_get_entity_tag_api_size"
	AllocateWarehouseBySalesOrderGroupsKey            = "allocate_warehouse_by_sales_order_groups"
	SupportSellerMultiWhPFFKey                        = "support_seller_multi_wh_pff"
	SellerMultiWHWithPartialFBSKey                    = "seller_multi_wh_with_partial_fbs"
	EnableAllocateWarehouseBySalesOrdersKey           = "enable_allocate_warehouse_by_sales_orders"
)
