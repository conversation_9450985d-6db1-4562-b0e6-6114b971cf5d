package business_config

import (
	abconfig "git.garena.com/shopee/experiment-platform/abtest-core/v2/api/config"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/utils"
)

var (
	migrationConfig                                      = utils.NewRecordConfig[MigrationConfig](MigrationKey)
	igs3PFShopFlagsConfig                                = utils.NewJsonRecordConfig[[]uint64](IGS3PFShopFlagsKey)
	igsLFFShopFlags                                      = utils.NewJsonRecordConfig[[]uint64](IGSLFFShopFlagsKey, utils.WithDefaultValue([]uint64{}))
	lffWarehouseRegionPriority                           = utils.NewJsonRecordConfig[[]string](LFFWarehouseRegionPriorityKey, utils.WithDefaultValue([]string{}))
	igsDynamicConfig                                     = utils.NewRecordConfig[IGSDynamicConfig](IGSDynamicConfigKey)
	enableWarehouseRegionForAddressAPIConfig             = utils.NewBoolRecordConfig[bool](EnableWarehouseRegionForAddressAPIKey)
	defaultSIPPrimaryRegionToDummyBuyerGeoLocationConfig = utils.NewRecordConfig[map[string]GeoLocationConfig](DefaultSIPPrimaryRegionToDummyBuyerGeoLocationKey)
	defaultBuyerGeoLocationConfig                        = utils.NewRecordConfig[GeoLocationConfig](DefaultBuyerGeoLocationKey)
	abTestingConfig                                      = utils.NewJsonRecordConfig[abconfig.Config](AbTestingKey)
	enableCachedSellerTagFlowConfig                      = utils.NewBoolRecordConfig[bool](EnableCachedSellerTagFlowKey)
	batchGetEntityTagAPISizeConfig                       = utils.NewIntRecordConfig[int](BatchGetEntityTagAPISizeKey)
	allocateWarehouseBySalesOrdersGroupConfig            = utils.NewRecordConfig[[]AllocateWarehouseBySalesOrdersGroup](AllocateWarehouseBySalesOrderGroupsKey)
	supportSellerMultiWhPFFConfig                        = utils.NewBoolRecordConfig[bool](SupportSellerMultiWhPFFKey)
	sellerMultiWhWithPartialFBSConfig                    = utils.NewRecordConfig[SellerMultiWhWithPartialFBSConfig](SellerMultiWHWithPartialFBSKey)
	enableAllocateWarehouseBySalesOrders                 = utils.NewBoolRecordConfig[bool](EnableAllocateWarehouseBySalesOrdersKey)
	enable3PFIgnoreSellerTagConfig                       = utils.NewBoolRecordConfig[bool](Enable3PFIgnoreSellerTagKey)
)

type (
	MigrationConfig struct {
		CompareConfig map[string]int `yaml:"compare"`
		SwitchConfig  map[string]int `yaml:"switch"`
	}

	SellerMultiWhWithPartialFBSConfig struct {
		SkipCheckABTest bool         `json:"skip_check_ab_test" yaml:"skip_check_ab_test"`
		ABTestConfig    ABTestConfig `json:"ab_test_config" yaml:"ab_test_config"`
	}

	ABTestConfig struct {
		// Input
		SceneKey    string                 `json:"scene_key" yaml:"scene_key"`
		FeatureKeys []string               `json:"feature_keys" yaml:"feature_keys"`
		BizName     string                 `json:"biz_name" yaml:"biz_name"`
		Params      map[string]interface{} `json:"params" yaml:"params"`

		// Result
		ResultKey            string   `json:"result_key,omitempty" yaml:"result_key,omitempty"`
		ResultTypeToCheckFor string   `json:"result_type_to_check_for,omitempty" yaml:"result_type_to_check_for"` // Default (String):"", JSON:"json"
		Value                string   `json:"value,omitempty" yaml:"value"`                                       // To be used when ResultTypeToCheckFor is string
		JsonTrueFields       []string `json:"json_true_fields,omitempty" yaml:"json_true_fields"`                 // To be used when ResultTypeToCheckFor is JSON
		JsonFalseFields      []string `json:"json_false_fields,omitempty" yaml:"json_false_fields"`               // To be used when ResultTypeToCheckFor is JSON
		Default              bool     `json:"default,omitempty" yaml:"default"`
	}

	AllocateWarehouseBySalesOrdersGroup struct {
		SellerTag                      string  `json:"seller_tag" yaml:"seller_tag"`
		CalculateFromPastDays          int     `json:"calculate_from_past_days" yaml:"calculate_from_past_days"`
		SellerWarehouseSalesOrdersMax  int     `json:"seller_warehouse_sales_orders_max" yaml:"seller_warehouse_sales_orders_max"`
		SellerWarehouseSalesOrdersMin  int     `json:"seller_warehouse_sales_orders_min" yaml:"seller_warehouse_sales_orders_min"`
		SellerWarehouseSalesOrderRatio float64 `json:"seller_warehouse_sales_order_ratio" yaml:"seller_warehouse_sales_order_ratio"`
	}
)
