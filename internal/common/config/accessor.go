package config

// 将 config 合入一个接口，这样只需要对 confAccessor mock

//go:generate mockgen --build_flags=--mod=mod -source=accessor.go -destination=accessor_mock.go -package=config -mock_names=ConfAccessor=MockConfAccessor

import (
	"context"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/application_config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/business_config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/mutable_application_config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/server_config"
)

type ConfAccessor interface {
	application_config.ApplicationAccessor
	business_config.BusinessAccessor
	mutable_application_config.MutableApplicationAccessor
	server_config.ServerAccessor

	Init(ctx context.Context) error
}

type ConfAccessorImpl struct {
	application_config.ApplicationAccessor
	business_config.BusinessAccessor
	mutable_application_config.MutableApplicationAccessor
	server_config.ServerAccessor
}

func (c *ConfAccessorImpl) Init(ctx context.Context) error {
	if err := c.ApplicationAccessor.Init(ctx); err != nil {
		return err
	}
	if err := c.BusinessAccessor.Init(ctx); err != nil {
		return err
	}
	if err := c.MutableApplicationAccessor.Init(ctx); err != nil {
		return err
	}
	if err := c.ServerAccessor.Init(ctx); err != nil {
		return err
	}
	return nil
}

func NewConfAccessorImpl(
	applicationAccessor application_config.ApplicationAccessor,
	businessAccessor business_config.BusinessAccessor,
	mutableApplicationAccessor mutable_application_config.MutableApplicationAccessor,
	serverAccessor server_config.ServerAccessor,
) *ConfAccessorImpl {
	return &ConfAccessorImpl{
		ApplicationAccessor:        applicationAccessor,
		BusinessAccessor:           businessAccessor,
		MutableApplicationAccessor: mutableApplicationAccessor,
		ServerAccessor:             serverAccessor,
	}
}
