package mutable_application_config

import (
	"context"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/utils"
)

type MutableApplicationAccessor interface {
	GetLruCacheConfig(ctx context.Context) LruCacheConfig
	GetLayerCacheConfig(ctx context.Context) LayerCacheConfig

	Init(ctx context.Context) error
}

func NewMutableApplicationAccessorImpl() *MutableApplicationAccessorImpl {
	confListener := utils.NewConfListener(mutableApplicationConfigPrefix, refreshConfigInterval, true)
	accessor := &MutableApplicationAccessorImpl{
		ConfListener: confListener,
	}
	return accessor
}

type MutableApplicationAccessorImpl struct {
	ConfListener utils.ConfListener
}

func (a *MutableApplicationAccessorImpl) GetLruCacheConfig(ctx context.Context) LruCacheConfig {
	return lruCacheConfig.ValueCtx(ctx).Get()
}

func (a *MutableApplicationAccessorImpl) GetLayerCacheConfig(ctx context.Context) LayerCacheConfig {
	return layerCacheConfig.ValueCtx(ctx).Get()
}

func (a *MutableApplicationAccessorImpl) Init(ctx context.Context) error {
	a.ConfListener.Register(lruCacheConfig)
	a.ConfListener.Register(layerCacheConfig)
	return a.ConfListener.Init(ctx)
}
