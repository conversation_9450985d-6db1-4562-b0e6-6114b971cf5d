package mutable_application_config

import (
	"context"

	"github.com/bytedance/sonic"
	jsoniter "github.com/json-iterator/go"

	"git.garena.com/shopee/bg-logistics/go/gocommon/logger"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/utils"
)

var (
	lruCacheConfig   = utils.NewRecordConfig[LruCacheConfig](LRUCacheKey, utils.WithRefreshFunc(lruCacheRefreshFuncAdapter))
	layerCacheConfig = utils.NewRecordConfig[LayerCacheConfig](LayerCacheConfigKey, utils.WithRefreshFunc(layerCacheRefreshFuncAdapter))
)

type (
	LruCacheConfig struct {
		Config string                   `yaml:"config"` // LRU缓存公共配置
		Param  map[string]LruCacheParam // 用于保存config反序列化的内容
	}

	LruCacheParam struct {
		Timeout int64 `json:"timeout"` // LRU缓存过期时间，此为数值，默认单位：秒（s）
		Size    int   `json:"size"`    // LRU缓存长度
		Enable  bool  `json:"enable"`
	}

	LayerCacheConfig struct {
		MemSize             int                                  `yaml:"MemSize" json:"MemSize"`
		MemNumOfCounters    int                                  `yaml:"MemNumOfCounters" json:"MemNumOfCounters"`
		MemLogicTTL         int                                  `yaml:"MemLogicTTL" json:"MemLogicTTL"`
		MemPhysicalTTL      int                                  `yaml:"MemPhysicalTTL" json:"MemPhysicalTTL"`
		PenetrationQpsLimit int                                  `yaml:"PenetrationQpsLimit" json:"PenetrateQpsLimit"`
		Expire              string                               `yaml:"Expire" json:"Expire"`
		Namespaces          map[string]LayerCacheNamespaceConfig `yaml:"namespaces" json:"namespaces"`
		ExpireConfig        map[string]LayerCacheExpireConfig    `yaml:"expire_config" json:"expire_config"`
	}

	LayerCacheNamespaceConfig struct {
		MemSize          int `yaml:"MemSize" json:"MemSize"`
		MemNumOfCounters int `yaml:"MemNumOfCounters" json:"MemNumOfCounters"`
		MemLogicTTL      int `yaml:"MemLogicTTL" json:"MemLogicTTL"`
		MemPhysicalTTL   int `yaml:"MemPhysicalTTL" json:"MemPhysicalTTL"`
	}

	LayerCacheExpireConfig struct {
		Namespace     string `json:"namespace" yaml:"namespace"`
		ExpireSeconds uint32 `json:"expire_seconds" yaml:"expire_seconds"`
		Timeout       uint32 `json:"timeout" yaml:"timeout"`
	}
)

func lruCacheRefreshFuncAdapter(ctx context.Context, key string, defaultValue LruCacheConfig) (value LruCacheConfig, err error) {
	// 首先使用默认的刷新函数加载配置
	value, err = utils.DefaultRefreshFunc(ctx, key, defaultValue)
	if err != nil {
		return value, err
	}

	// 然后将 Config 字段反序列化到 Param 字段
	if value.Config != "" {
		param := make(map[string]LruCacheParam)
		if err := sonic.UnmarshalString(value.Config, &param); err != nil {
			logger.LogErrorf("Unmarshal LruCacheConfig.Config failed|key=%s|config=%s|err=%v",
				key, value.Config, err.Error())
			return value, err
		}
		value.Param = param
	}

	return value, nil
}

func layerCacheRefreshFuncAdapter(ctx context.Context, key string, defaultValue LayerCacheConfig) (value LayerCacheConfig, err error) {
	value, err = utils.DefaultRefreshFunc(ctx, key, defaultValue)
	if err != nil {
		return value, err
	}
	expireConfigList := make([]LayerCacheExpireConfig, 0)
	err = jsoniter.UnmarshalFromString(value.Expire, &expireConfigList)
	if err != nil {
		logger.LogErrorf("UnmarshalFromString failed|config=%s", value)
		return value, err
	}
	for _, expireConfig := range expireConfigList {
		value.ExpireConfig[expireConfig.Namespace] = expireConfig
	}

	return value, nil
}
