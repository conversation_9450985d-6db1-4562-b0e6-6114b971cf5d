package spexlib

import (
	"context"

	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/account_address.pb"
	account_core64 "git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/account_core.pb"
	"github.com/gogo/protobuf/proto"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/meta"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

var (
	GetAccount = spexService{
		Command: GetAccountCommand,
		System:  constant.SystemAccount,
	}

	GetPrivateAddressWithGeocoding = spexService{
		Command: GetPrivateAddressWithGeocodingCommand,
		System:  constant.SystemAccount,
	}
)

func (client *SpexClientImpl) GetAccountDetail(ctx context.Context, userId typ.UserIdType, region meta.Region) (*account_core64.UserDetail, fsserr.Error) {
	req := &account_core64.GetAccountRequest{
		Userid:    proto.Int64(userId),
		Region:    proto.String(string(region)),
		QueryFlag: proto.Int32(int32(account_core64.Constant_ACCOUNT_QUERY_FLAG_MAIN)),
	}
	var resp *account_core64.GetAccountResponse
	if spexErr := client.request(ctx, GetAccount, req, resp); spexErr != nil {
		return nil, fsserr.With(fsserr.SpexError, spexErr).WithReference(newReference(GetAccount, spexErr))
	}
	if resp.GetUser() == nil {
		return nil, fsserr.New(fsserr.SpexError, "user %d not found", userId)
	}
	return resp.GetUser(), nil
}

func (client *SpexClientImpl) GetPrivateAddressWithGeocoding(ctx context.Context, userId typ.UserIdType, addressID uint64) (*account_address.PrivateAddressGeoCoded, fsserr.Error) {
	req := &account_address.GetPrivateAddressWithGeocodingRequest{
		Region:    proto.String(envvar.GetCID(ctx)),
		Userid:    proto.Int64(userId),
		AddressId: proto.Int64(int64(addressID)),
	}
	var resp *account_address.GetPrivateAddressWithGeocodingResponse
	if spexErr := client.request(ctx, GetPrivateAddressWithGeocoding, req, resp); spexErr != nil {
		return nil, fsserr.With(fsserr.SpexError, spexErr).WithReference(newReference(GetPrivateAddressWithGeocoding, spexErr))
	}
	if resp.GetAddress() == nil {
		return nil, fsserr.New(fsserr.SpexError, "user %d address %d not found", userId, addressID)
	}
	return resp.GetAddress(), nil

}
