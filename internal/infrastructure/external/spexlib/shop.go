package spexlib

import (
	"context"
	"strconv"

	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/marketplace_order_processing_cb_collection_api.pb"
	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/shop_core.pb"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

var (
	GetShopBatch = spexService{
		Command: GetShopBatchCommand,
		System:  constant.SystemShop,
	}

	BatchGetShopSipBasic = spexService{
		Command: BatchGetShopSipBasicCommand,
		System:  constant.SystemShop,
	}
)

func (client *SpexClientImpl) GetShopBatchWithCache(ctx context.Context, shopIds []int64) ([]*shop_core.ShopDetail, fsserr.Error) {
	return client.GetShopBatch(ctx, shopIds)
}

func (client *SpexClientImpl) GetShopBatch(ctx context.Context, shopIds []int64) ([]*shop_core.ShopDetail, fsserr.Error) {
	req := &shop_core.GetShopBatchRequest{
		ShopidList: shopIds,
	}
	var resp shop_core.GetShopBatchResponse
	if spexErr := client.request(ctx, GetShopBatch, req, resp); spexErr != nil {
		return nil, fsserr.With(fsserr.SpexError, spexErr).WithReference(newReference(GetShopBatch, spexErr))
	}
	return resp.GetShopList(), nil
}

func (client *SpexClientImpl) GetDummyBuyerId(ctx context.Context, cid string) (*marketplace_order_processing_cb_collection_api.GetDummyBuyerIdResponse, fsserr.Error) {
	var (
		req  = &marketplace_order_processing_cb_collection_api.GetDummyBuyerIdRequest{}
		resp *marketplace_order_processing_cb_collection_api.GetDummyBuyerIdResponse
	)
	if spexErr := client.request(ctx, GetDummyBuyerId, req, resp, WithCID(cid)); spexErr != nil {
		return nil, fsserr.With(fsserr.SpexError, spexErr).WithReference(newReference(GetDummyBuyerId, spexErr))
	}
	return resp, nil
}

func getShopDetailCacheKey(shopID uint64) string {
	return "shop.detail." + strconv.FormatUint(shopID, 10)
}
