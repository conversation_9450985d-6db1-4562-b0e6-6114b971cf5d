package spexlib

import (
	"context"
	"strconv"

	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/account_address.pb"
	account_core64 "git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/account_core.pb"
	location_user_location64 "git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/location_user_location.pb"
	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/marketplace_listing_itemtagservice_querying_api.pb"
	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/price_checkout_promo.pb"
	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/seller_seller_address_core.pb"
	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/seller_seller_tag_core.pb"
	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/shop_core.pb"

	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/marketplace_order_processing_cb_collection_api.pb"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/meta"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

var (
	GetCheckoutStockInfo = spexService{
		Command: GetCheckoutStockInfoCommand,
		System:  constant.SystemPrice,
	}

	GetShippingAddressCombinations = spexService{
		Command: GetShippingAddressCombinationsCommand,
		System:  constant.SystemUserLocation,
	}

	GetItemLabels = spexService{
		Command: GetItemLabelsCommand,
		System:  constant.SystemMarketplace,
	}

	GetModelLabels = spexService{
		Command: GetModelLabelsCommand,
		System:  constant.SystemMarketplace,
	}

	GetDummyBuyerId = spexService{
		Command: GetDummyBuyerIdCommand,
		System:  constant.SystemMarketplace,
	}
)

type PriceSpexClient interface {
	GetCheckoutStockInfo(ctx context.Context, request *price_checkout_promo.GetCheckoutStockInfoRequest) ([]*price_checkout_promo.CheckoutOrderStockInfo, fsserr.Error)
}

type AccountSpexClient interface {
	GetAccountDetail(ctx context.Context, userId typ.UserIdType, region meta.Region) (*account_core64.UserDetail, fsserr.Error)
	GetPrivateAddressWithGeocoding(ctx context.Context, userId typ.UserIdType, addressID uint64) (*account_address.PrivateAddressGeoCoded, fsserr.Error)
}

type LocationSpexClient interface {
	GetShippingAddresses(ctx context.Context, req *location_user_location64.GetShippingAddressCombinationsRequest) (*location_user_location64.GetShippingAddressCombinationsResponse, fsserr.Error)
}

type SellerSpexClient interface {
	BatchGetEntityTagValue(ctx context.Context, req *seller_seller_tag_core.BatchGetEntityTagValueRequest) ([]*seller_seller_tag_core.EntityTag, fsserr.Error)
	BatchGetEntityTag(ctx context.Context, req *seller_seller_tag_core.BatchGetEntityTagRequest) ([]*seller_seller_tag_core.EntityTag, fsserr.Error)
	BatchGetWarehouseFlagByShop(ctx context.Context, shopIds []int64) ([]*seller_seller_address_core.ShopWarehouseFlag, fsserr.Error)
	BatchGetWarehouseByShopWithoutPagination(ctx context.Context, shopIds []int64) ([]*seller_seller_address_core.ShopWarehouse, fsserr.Error)

	GetShopBatch(ctx context.Context, shopIds []int64) ([]*shop_core.ShopDetail, fsserr.Error)

	GetItemLabels(ctx context.Context, req *marketplace_listing_itemtagservice_querying_api.GetItemLabelsRequest) ([]*marketplace_listing_itemtagservice_querying_api.ItemLabel, fsserr.Error)
	GetModelLabels(ctx context.Context, req *marketplace_listing_itemtagservice_querying_api.GetModelLabelsRequest) ([]*marketplace_listing_itemtagservice_querying_api.ModelLabel, fsserr.Error)
}

type ShopSpexClient interface {
	BatchGetShopSipBasic(ctx context.Context, req *shop_core.BatchGetShopSipBasicRequest) (*shop_core.BatchGetShopSipBasicResponse, fsserr.Error)
	BatchGetShop(ctx context.Context, req *shop_core.GetShopBatchRequest) (*shop_core.GetShopBatchResponse, fsserr.Error)
	BatchGetWarehouseFlagByShop(ctx context.Context, req *seller_seller_address_core.BatchGetWarehouseFlagByShopRequest) (*seller_seller_address_core.BatchGetWarehouseFlagByShopResponse, fsserr.Error)
	BatchGetWarehouseByShopWithoutPagination(ctx context.Context, req *seller_seller_address_core.BatchGetWarehouseByShopWithoutPaginationRequest) (*seller_seller_address_core.BatchGetWarehouseByShopWithoutPaginationResponse, fsserr.Error)
}

type MarketplaceSpexClient interface {
	GetDummyBuyerId(ctx context.Context, cid string) (*marketplace_order_processing_cb_collection_api.GetDummyBuyerIdResponse, fsserr.Error)
}

type SpexClient interface {
	PriceSpexClient
	AccountSpexClient
	LocationSpexClient
	SellerSpexClient
	ShopSpexClient
	MarketplaceSpexClient
}

func NewSpexClientImpl(confAccessor config.ConfAccessor) *SpexClientImpl {
	return &SpexClientImpl{
		ConfAccessor: confAccessor,
	}
}

type SpexClientImpl struct {
	ConfAccessor config.ConfAccessor
}

func (client *SpexClientImpl) GetCheckoutStockInfo(ctx context.Context, request *price_checkout_promo.GetCheckoutStockInfoRequest) ([]*price_checkout_promo.CheckoutOrderStockInfo, fsserr.Error) {
	var resp *price_checkout_promo.GetCheckoutStockInfoResponse
	if spexErr := client.request(ctx, GetCheckoutStockInfo, request, resp); spexErr != nil {
		// todo enrich error
		return nil, fsserr.With(fsserr.SpexError, spexErr).WithReference(newReference(GetCheckoutStockInfo, spexErr))
	}
	return resp.GetCheckoutOrderStockInfo(), nil
}

func (client *SpexClientImpl) GetShippingAddresses(ctx context.Context, req *location_user_location64.GetShippingAddressCombinationsRequest) (*location_user_location64.GetShippingAddressCombinationsResponse, fsserr.Error) {
	var resp *location_user_location64.GetShippingAddressCombinationsResponse
	if spexErr := client.request(ctx, GetShippingAddressCombinations, req, resp); spexErr != nil {
		return nil, fsserr.With(fsserr.SpexError, spexErr).WithReference(newReference(GetShippingAddressCombinations, spexErr))
	}
	return resp, nil
}

func (client *SpexClientImpl) GetItemLabels(ctx context.Context, req *marketplace_listing_itemtagservice_querying_api.GetItemLabelsRequest) ([]*marketplace_listing_itemtagservice_querying_api.ItemLabel, fsserr.Error) {
	var resp *marketplace_listing_itemtagservice_querying_api.GetItemLabelsResponse
	if spexErr := client.request(ctx, GetItemLabels, req, resp); spexErr != nil {
		return nil, fsserr.With(fsserr.SpexError, spexErr).WithReference(newReference(GetItemLabels, spexErr))
	}
	return resp.GetItemLabelList(), nil
}

func (client *SpexClientImpl) GetModelLabels(ctx context.Context, req *marketplace_listing_itemtagservice_querying_api.GetModelLabelsRequest) ([]*marketplace_listing_itemtagservice_querying_api.ModelLabel, fsserr.Error) {
	var resp *marketplace_listing_itemtagservice_querying_api.GetModelLabelsResponse
	if spexErr := client.request(ctx, GetModelLabels, req, resp); spexErr != nil {
		return nil, fsserr.With(fsserr.SpexError, spexErr).WithReference(newReference(GetModelLabels, spexErr))
	}
	return resp.GetModelLabelList(), nil
}

func (client *SpexClientImpl) BatchGetShopSipBasic(ctx context.Context, req *shop_core.BatchGetShopSipBasicRequest) (*shop_core.BatchGetShopSipBasicResponse, fsserr.Error) {
	var resp *shop_core.BatchGetShopSipBasicResponse
	if spexErr := client.request(ctx, BatchGetShopSipBasic, req, resp); spexErr != nil {
		return nil, fsserr.With(fsserr.SpexError, spexErr).WithReference(newReference(BatchGetShopSipBasic, spexErr))
	}
	return resp, nil
}

func (client *SpexClientImpl) BatchGetShop(ctx context.Context, req *shop_core.GetShopBatchRequest) (*shop_core.GetShopBatchResponse, fsserr.Error) {
	var resp *shop_core.GetShopBatchResponse
	if spexErr := client.request(ctx, GetShopBatch, req, resp); spexErr != nil {
		return nil, fsserr.With(fsserr.SpexError, spexErr).WithReference(newReference(GetShopBatch, spexErr))
	}

	return resp, nil
}

func newReference(service spexService, spexError *spexError) fsserr.Reference {
	if spexError == nil {
		return fsserr.Reference{}
	}
	return fsserr.Reference{
		Source:        service.System,
		SourceCode:    strconv.Itoa(int(spexError.Code())),
		SourceMessage: spexError.Error(),
	}
}
