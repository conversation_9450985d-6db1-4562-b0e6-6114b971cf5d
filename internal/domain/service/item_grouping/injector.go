package item_grouping

import "github.com/google/wire"

var (
	ItemGrouperProviderSet = wire.NewSet(
		NewItemGrouper,
		wire.Bind(new(ItemGrouper), new(*DefaultItemGrouper)),
	)

	ShippingOrderProcessorProviderSet = wire.NewSet(
		NewAdvanceBookingShippingOrderProcessor,
		NewCBLFFShippingOrderProcessor,
		NewPffShippingOrderProcessor,
		NewCb3pfShippingOrderProcessor,
		NewShopeeShippingOrderProcessor,
	)

	ItemOrganizerProviderSet = wire.NewSet(
		NewItemOrganizer,
		wire.Bind(new(ItemOrganizer), new(*ItemOrganizerImpl)),
		NewMinimumParcelStrategyItemOrganizer,
		NewWarehousePriorityStrategyItemOrganizer,
	)
)
