package item_grouping

import (
	"context"
	"sort"
	"strings"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/collection"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/address"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/soc"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/warehouse_priority"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/shared"
)

type AdvanceBookingShippingOrderProcessor struct {
	socService               soc.SOCService
	warehousePriorityService warehouse_priority.WarehousePriorityService
	addressService           address.AddrService
}

func NewAdvanceBookingShippingOrderProcessor(
	socService soc.SOCService,
	warehousePriorityService warehouse_priority.WarehousePriorityService,
	addressService address.AddrService,
) *AdvanceBookingShippingOrderProcessor {
	return &AdvanceBookingShippingOrderProcessor{
		socService:               socService,
		warehousePriorityService: warehousePriorityService,
		addressService:           addressService,
	}
}

func (c *AdvanceBookingShippingOrderProcessor) Name() ShippingOrderProcessorName {
	return AdvanceBookingShippingOrderProcessorName
}

func (c *AdvanceBookingShippingOrderProcessor) IsAbleToProcess(ctx context.Context, shippingOrder ShippingOrder) bool {
	return shippingOrder.RequireAdvanceBooking && !shippingOrder.IsSourceSelected() &&
		!shippingOrder.FromMultipleShop() && shippingOrder.ShopInfo().AdvanceBookingFulfilmentCapability
}

func (c *AdvanceBookingShippingOrderProcessor) DoProcess(
	ctx context.Context,
	buyerInfo BuyerInfo,
	shippingOrders []ShippingOrder,
) ([]ShippingOrder, error) {
	//fs := metrics.StartFunctionSpan("itemgrouping.AdvanceBookingShippingOrderProcessor.DoProcess")
	//defer fs.Finish(ctx)

	// Get all socIDs and check serviceability
	socIDSet := collection.NewSet[string]()
	for _, shippingOrder := range shippingOrders {
		for _, item := range shippingOrder.Items {
			for _, location := range item.AdvanceBookingStocks() {
				socIDSet.Add(location.Source)
			}
			for _, location := range item.CacheWarehouseStocks() {
				socIDSet.Add(location.Source)
			}
		}
	}
	socToServiceability, err := c.socService.CheckSOCLocationsServiceabilityV2(ctx, socIDSet.Values(), buyerInfo.Address)
	if err != nil {
		//logger.Error(ctx, "CheckSOCLocationsServiceabilityV2Error", ulog.Error(err))
	}

	var processedOrders []ShippingOrder
	for _, shippingOrder := range shippingOrders {
		sourceToItems, ok := c.assignItemStock(ctx, shippingOrder.Items, socToServiceability)
		if !ok {
			processedOrders = append(processedOrders, shippingOrder)
			continue
		}

		for key, items := range sourceToItems {
			newShippingOrder := shippingOrder.SplitNew(items)
			newShippingOrder.FulfilmentInfo.Source = key.FulfilmentSource
			newShippingOrder.FulfilmentInfo.Type = key.FulfilmentType
			if key.FulfilmentType == shared.FulfilmentTypeAdvanceBooking {
				newShippingOrder.CacheFallbackFulfilmentInfo = c.getCacheSellerFallback(ctx, items)
				newShippingOrder.FulfilmentInfo.AbleToFallbackSellerStock = newShippingOrder.CacheFallbackFulfilmentInfo.Exists()
			} else if key.FulfilmentType == shared.FulfilmentTypeCacheWarehouse {
				newShippingOrder.CacheFallbackFulfilmentInfo, err = c.getCacheWarehouseFallback(ctx, items, buyerInfo.Address)
				if err != nil {
					return nil, err
				}
			}
			processedOrders = append(processedOrders, newShippingOrder)
		}
		//metrics.GenerateIGSOrderMetrics(ctx, metrics.IGSOrderTypeAdvanceBooking, len(sourceToItems))
	}

	return processedOrders, nil
}

// assignItemStock selects the stock location for the item based on the serviceability of the stock locations.
func (c *AdvanceBookingShippingOrderProcessor) assignItemStock(
	ctx context.Context,
	items []ShippingOrderItem,
	socToServiceability map[string]bool,
) (map[OrganizeItemKey][]ShippingOrderItem, bool) {
	// Business requirement: Only one item in the order, and the quantity is 1.
	if len(items) != 1 {
		return nil, false
	}
	item := items[0]
	if item.Quantity != 1 {
		return nil, false
	}
	if len(item.PackageItems) > 0 {
		return nil, false
	}
	stockLocations := item.AdvanceBookingStocks()
	stockLocations = append(stockLocations, item.CacheWarehouseStocks()...)
	if stockLocations.TotalStock() == 0 {
		return nil, false
	}

	var serviceableStockLocations []ItemStockLocation
	for _, location := range stockLocations {
		if socToServiceability[location.Source] {
			serviceableStockLocations = append(serviceableStockLocations, location)
		}
	}
	if len(serviceableStockLocations) == 0 {
		return nil, false
	}

	sort.Slice(serviceableStockLocations, func(i, j int) bool {
		// cache order phase 2: prioritise cache_warehouse (8) > cache_seller (4)
		typeI, typeJ := serviceableStockLocations[i].FulfilmentType, serviceableStockLocations[j].FulfilmentType
		if typeI != typeJ {
			return typeI > typeJ
		}
		return serviceableStockLocations[i].Source < serviceableStockLocations[j].Source
	})
	itemKey := OrganizeItemKey{
		FulfilmentSource: serviceableStockLocations[0].Source,
		FulfilmentType:   serviceableStockLocations[0].FulfilmentType,
	}
	sourceToItems := map[OrganizeItemKey][]ShippingOrderItem{itemKey: {item}}

	return sourceToItems, true
}

func (c *AdvanceBookingShippingOrderProcessor) checkAbleToFallbackToSellerStock(items []ShippingOrderItem) bool {
	for _, item := range items {
		if item.SellerStocks().TotalStock() < item.Quantity {
			return false
		}
	}
	return true
}

func (c *AdvanceBookingShippingOrderProcessor) getCacheSellerFallback(ctx context.Context, items []ShippingOrderItem) typ.Optional[OrderFulfilmentInfo] {
	// currently, cache order only have 1 SKU with 1 quantity
	item := items[0]
	for _, stock := range item.SellerStocks() {
		if stock.Source == strings.ToUpper(envvar.GetCID(ctx)+"Z") && stock.AvailableStock >= item.Quantity {
			return typ.NewOptional(OrderFulfilmentInfo{
				Source: stock.Source,
				Type:   shared.FulfilmentTypeSeller,
			})
		}
	}
	return typ.Empty[OrderFulfilmentInfo]()
}

func (c *AdvanceBookingShippingOrderProcessor) getCacheWarehouseFallback(ctx context.Context, items []ShippingOrderItem, buyerAddr entity.ItemGroupBuyerAddress) (typ.Optional[OrderFulfilmentInfo], error) {
	// currently, cache order only have 1 SKU with 1 quantity
	item := items[0]
	whSources := make([]string, 0)
	for _, stock := range item.ShopeeStocks() {
		if stock.AvailableStock >= item.Quantity {
			whSources = append(whSources, stock.Source)
		}
	}
	if len(whSources) == 0 {
		return typ.Empty[OrderFulfilmentInfo](), nil
	}
	fallbackSource := whSources[0]
	if len(whSources) > 1 {
		if prioritizedWarehouses, err := c.warehousePriorityService.PrioritizeShopeeWarehouse(ctx, buyerAddr, whSources); err != nil {
			//logger.Error(ctx, "fail get shopee warehouse priority", ulog.Error(err), ulog.Any("warehouses", whSources))
		} else {
			fallbackSource = prioritizedWarehouses[0]
		}
	}

	fallbackAddress, err := c.addressService.GetShopeeWarehouseAddress(ctx, fallbackSource)
	if err != nil {
		//logger.Error(ctx, "fail get fallback shopee warehouse address", ulog.Error(err), ulog.String("fallback_source", fallbackSource))
		return typ.Empty[OrderFulfilmentInfo](), err
	}

	return typ.NewOptional(OrderFulfilmentInfo{
		Source:    fallbackSource,
		Type:      shared.FulfilmentTypeShopee,
		AddressID: fallbackAddress.AddressID,
	}), nil
}
