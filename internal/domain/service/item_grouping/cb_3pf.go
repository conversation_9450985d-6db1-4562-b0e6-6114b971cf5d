package item_grouping

import (
	"context"
	"slices"
	"sort"
	"strings"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/envvar"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/metrics"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/collection"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/address"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/shop"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/shop_local_sip"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/warehouse_priority"
	Logger "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/logger"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/shared"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

type Cb3pfShippingOrderProcessor struct {
	confAccessor             config.ConfAccessor
	itemOrganizer            ItemOrganizer
	shopService              shop.ShopService
	addressService           address.AddrService
	warehousePriorityService warehouse_priority.WarehousePriorityService
	shopLocalSIPService      shop_local_sip.ShopLocalSIPService
}

func NewCb3pfShippingOrderProcessor(confAccessor config.ConfAccessor,
	itemOrganizer ItemOrganizer,
	shopService shop.ShopService,
	addressService address.AddrService,
	warehousePriorityService warehouse_priority.WarehousePriorityService,
	shopLocalSIPService shop_local_sip.ShopLocalSIPService,
) *Cb3pfShippingOrderProcessor {
	return &Cb3pfShippingOrderProcessor{
		confAccessor:             confAccessor,
		itemOrganizer:            itemOrganizer,
		shopService:              shopService,
		addressService:           addressService,
		warehousePriorityService: warehousePriorityService,
		shopLocalSIPService:      shopLocalSIPService,
	}
}

func (c *Cb3pfShippingOrderProcessor) Name() ShippingOrderProcessorName {
	return CB3PFShippingOrderProcessorName
}

func (c *Cb3pfShippingOrderProcessor) IsAbleToProcess(ctx context.Context, shippingOrder ShippingOrder) bool {
	return !shippingOrder.IsSourceSelected() && !shippingOrder.FromMultipleShop() &&
		slices.Contains(c.confAccessor.GetIGS3PFShopFlags(ctx), shippingOrder.ShopInfo().ShopFlag)
}

func (c *Cb3pfShippingOrderProcessor) DoProcess(ctx context.Context, buyerInfo BuyerInfo, shippingOrders []ShippingOrder) ([]ShippingOrder, error) {
	if c.confAccessor.GetEnable3PFIgnoreSellerTag(ctx) {
		return c.doProcessWithoutSellerTag(ctx, buyerInfo, shippingOrders)
	} else {
		return c.doProcessWithSellerTag(ctx, shippingOrders)
	}
}

func (c *Cb3pfShippingOrderProcessor) doProcessWithoutSellerTag(
	ctx context.Context,
	buyerInfo BuyerInfo,
	shippingOrders []ShippingOrder,
) ([]ShippingOrder, error) {
	cb3PFHybridOrders, cb3PFOnlyOrders, err := c.separateShippingOrders(shippingOrders)
	if err != nil {
		return nil, err
	}

	var processedOrders []ShippingOrder
	for i, order := range append(cb3PFHybridOrders, cb3PFOnlyOrders...) {
		splitOrders, err := c.splitShippingOrderWithoutSellerTag(
			ctx,
			buyerInfo,
			order,
			i < len(cb3PFHybridOrders),
		)
		if err != nil {
			return nil, err
		}
		processedOrders = append(processedOrders, splitOrders...)
	}

	return processedOrders, nil
}

func (c *Cb3pfShippingOrderProcessor) doProcessWithSellerTag(
	ctx context.Context,
	shippingOrders []ShippingOrder,
) ([]ShippingOrder, error) {

	shopIDSet := collection.NewSet[uint64]()
	for _, order := range shippingOrders {
		shopIDSet.Add(order.ShopInfo().ShopID)
	}
	shopIDs := shopIDSet.Values()

	shopIDToStrategy := c.getShopStrategy(ctx, shopIDs)
	cb3PFHybridOrders, cb3PFOnlyOrders, err := c.separateShippingOrders(shippingOrders)
	if err != nil {
		return nil, err
	}

	var processedOrders []ShippingOrder
	for i, order := range append(cb3PFHybridOrders, cb3PFOnlyOrders...) {
		splitOrders, err := c.splitShippingOrderWithSellerTag(
			ctx,
			order,
			shopIDToStrategy[order.ShopInfo().ShopID],
			i < len(cb3PFHybridOrders),
		)
		if err != nil {
			return nil, err
		}
		processedOrders = append(processedOrders, splitOrders...)
	}

	return processedOrders, nil

}

func (c *Cb3pfShippingOrderProcessor) separateShippingOrders(
	shippingOrders []ShippingOrder,
) (cb3PFHybridOrders []ShippingOrder, cb3PFOnlyOrders []ShippingOrder, err error) {
	for _, order := range shippingOrders {
		if len(order.ShopInfo().Warehouses) == 0 {
			cb3PFOnlyOrders = append(cb3PFOnlyOrders, order)
		} else {
			cb3PFHybridOrders = append(cb3PFHybridOrders, order)
		}
		continue
	}
	return
}

func (c *Cb3pfShippingOrderProcessor) getShopStrategy(
	ctx context.Context,
	shopIDs []uint64,
) map[uint64]shared.ItemOrganizeStrategy {
	return c.warehousePriorityService.BatchGetItemOrganizeStrategy(
		ctx,
		shopIDs,
		shared.ItemOrganizeStrategyWarehousePriority,
	)
}

func (c *Cb3pfShippingOrderProcessor) splitShippingOrderWithoutSellerTag(
	ctx context.Context,
	buyerInfo BuyerInfo,
	shippingOrder ShippingOrder,
	is3PFHybrid bool,
) ([]ShippingOrder, error) {
	regionToItems := make(map[string][]ShippingOrderItem)
	for _, item := range shippingOrder.Items {
		regionToItem, err := c.splitItemByRegion(ctx, item)
		if err != nil {
			return nil, err
		}
		for region, item := range regionToItem {
			regionToItems[region] = append(regionToItems[region], item)
		}
	}

	var splitOrders []ShippingOrder
	for region, items := range regionToItems {
		keyToItems, err := c.itemOrganizer.OrganizeItems(
			ctx,
			items,
			c.selectSourceFuncWithoutSellerTag(ctx, region),
			c.sourcePrioritizeFuncWithoutSellerTag(ctx, buyerInfo, shippingOrder.ShopInfo()),
			shared.ItemOrganizeStrategyMinimumParcel,
		)
		if err != nil {
			return nil, err
		}

		sourceToAddressID := make(map[string]uint64)
		for _, warehouse := range shippingOrder.ShopInfo().Warehouses {
			sourceToAddressID[warehouse.LocationID] = warehouse.AddressID
		}
		shippingOrders := splitShippingOrderByOrganizedItems(shippingOrder, keyToItems, sourceToAddressID)

		splitOrders = append(splitOrders, shippingOrders...)
	}

	c.generateMetrics(ctx, is3PFHybrid, len(splitOrders))
	return splitOrders, nil
}

func (c *Cb3pfShippingOrderProcessor) splitShippingOrderWithSellerTag(
	ctx context.Context,
	shippingOrder ShippingOrder,
	strategy shared.ItemOrganizeStrategy,
	is3PFHybrid bool,
) ([]ShippingOrder, error) {
	sourceToShopWarehouse := make(map[string]entity.ShopWarehouse)
	sourceToAddressID := make(map[string]uint64)
	for _, warehouse := range shippingOrder.ShopInfo().Warehouses {
		sourceToShopWarehouse[warehouse.LocationID] = warehouse
		sourceToAddressID[warehouse.LocationID] = warehouse.AddressID
	}

	keyToItems, err := c.itemOrganizer.OrganizeItems(
		ctx,
		shippingOrder.Items,
		c.selectSourceFuncWithSellerTag(ctx, is3PFHybrid, sourceToShopWarehouse),
		c.sourcePrioritizeFuncWithSellerTag(ctx, is3PFHybrid, sourceToShopWarehouse),
		strategy,
	)
	if err != nil {
		return nil, err
	}

	splitOrders := splitShippingOrderByOrganizedItems(shippingOrder, keyToItems, sourceToAddressID)
	c.generateMetrics(ctx, is3PFHybrid, len(splitOrders))
	return splitOrders, nil
}

func (c *Cb3pfShippingOrderProcessor) splitItemByRegion(ctx context.Context, item ShippingOrderItem) (map[string]ShippingOrderItem, error) {
	regionToTotalStock := make(map[string]uint32)
	var totalStock uint32
	for _, location := range item.SellerStocks() {
		region := c.getSourceRegion(location.Source)
		if region == "" {
			Logger.CtxLogErrorf(ctx, "invalid source region, item: %s, source: %s", Logger.JsonString(item), location.Source)
			continue
		}
		regionToTotalStock[region] += location.AvailableStock
		totalStock += location.AvailableStock
	}
	if totalStock < item.Quantity {
		return nil, &fsserr.OutOfStockError{
			ItemStockInfos: buildOOSItemStockInfos(item, totalStock),
		}
	}

	regionToItem := make(map[string]ShippingOrderItem)
	remainingStock := item.Quantity
	localRegion := []string{envvar.GetCID(ctx)}
	for _, region := range localRegion {
		if remainingStock <= 0 {
			break
		}

		reducingStock := regionToTotalStock[region]
		if reducingStock <= 0 {
			continue
		}
		reducingStock = min(reducingStock, remainingStock)
		remainingStock -= reducingStock

		newItem := item
		newItem.Quantity = reducingStock
		regionToItem[region] = newItem
	}

	if remainingStock > 0 {
		newItem := item
		newItem.Quantity = remainingStock
		regionToItem[""] = newItem
	}

	return regionToItem, nil
}

func (c *Cb3pfShippingOrderProcessor) getSourceRegion(source string) string {
	if len(source) < 2 {
		return ""
	}
	return strings.ToUpper(source[:2])
}

func (c *Cb3pfShippingOrderProcessor) selectSourceFuncWithoutSellerTag(ctx context.Context, region string) ItemOrganizeSourceSelectFunc {
	return func(item ShippingOrderItem) (StockLocations, error) {
		sellerStocks := c.filterLocationsByRegion(ctx, item.SellerStocks(), region)
		return sellerStocks, nil
	}
}

func (c *Cb3pfShippingOrderProcessor) filterLocationsByRegion(ctx context.Context, locations StockLocations, region string) StockLocations {
	var filteredLocations StockLocations
	for _, location := range locations {
		sourceRegion := c.getSourceRegion(location.Source)
		localRegion := envvar.GetCID(ctx)
		if region == "" {
			if sourceRegion != localRegion {
				filteredLocations = append(filteredLocations, location)
			}
		} else {
			if sourceRegion == localRegion {
				filteredLocations = append(filteredLocations, location)
			}
		}
	}
	return filteredLocations
}

func (c *Cb3pfShippingOrderProcessor) selectSourceFuncWithSellerTag(
	ctx context.Context,
	is3PFHybrid bool,
	sourceToShopWarehouse map[string]entity.ShopWarehouse,
) ItemOrganizeSourceSelectFunc {
	return func(item ShippingOrderItem) (StockLocations, error) {
		if is3PFHybrid {
			var validateStock []ItemStockLocation
			var localStockCount int
			for _, location := range item.SellerStocks() {
				if sourceToShopWarehouse[location.Source].AddressID != 0 {
					validateStock = append(validateStock, location)
					if sourceToShopWarehouse[location.Source].IsLocalWarehouse(ctx) {
						localStockCount++
					}
				}
			}

			if len(validateStock) == 0 {
				Logger.CtxLogErrorf(ctx, "3PF hybrid item has no valid stock, item: %s", Logger.JsonString(item))
				return nil, fsserr.Err3PFShopNoWH
			}
			if localStockCount > 1 {
				Logger.CtxLogErrorf(ctx, "3PF hybrid item has more than 1 local stock, item: %s", Logger.JsonString(item))
				return nil, fsserr.Err3PFShopNoWH
			}

			return validateStock, nil
		}

		if len(item.SellerStocks()) != 1 {
			Logger.CtxLogErrorf(ctx, "3PF only item should only have 1 stock, item: %s", Logger.JsonString(item))
			return nil, fsserr.Err3PFShopNoWH
		}

		return item.SellerStocks(), nil
	}
}

func (c *Cb3pfShippingOrderProcessor) sourcePrioritizeFuncWithoutSellerTag(
	ctx context.Context,
	buyerInfo BuyerInfo,
	shopInfo ShopInfo,
) ItemOrganizeSourcePrioritizeFunc {
	return func(sources []string) ([]string, error) {
		if len(sources) <= 1 {
			return sources, nil
		}
		buyerAddressGeo, err := c.getBuyerGeoLocation(ctx, buyerInfo, shopInfo.ShopID)
		if err != nil {
			return nil, err
		}
		return c.warehousePriorityService.PrioritizeSellerMultiWarehouse(
			ctx,
			buyerAddressGeo,
			shopInfo.SellerID,
			shopInfo.Warehouses,
			sources,
		), nil
	}
}

// only need the buyer address Geo for multi-warehouse shop
// for SIP affiliated shop, we need the dummy buyer Geo instead of real buyer Geo
func (c *Cb3pfShippingOrderProcessor) getBuyerGeoLocation(
	ctx context.Context,
	buyerInfo BuyerInfo,
	shopID uint64,
) (entity.GeoLocation, error) {
	localSIPInfo, err := c.shopLocalSIPService.GetShopLocalSIPInfo(ctx, shopID)
	if err != nil {
		Logger.CtxLogErrorf(ctx, "get local SIP shop info error: %v", err)
	}
	var buyerAddressGeo entity.GeoLocation

	if localSIPInfo.IsSIPAffiliated {
		buyerAddressGeo, err = c.addressService.GetDummyBuyerGeoLocation(ctx, localSIPInfo.DummyBuyerID, localSIPInfo.SIPPrimaryRegion)
	} else {
		buyerAddressGeo, err = c.addressService.GetBuyerGeoLocation(ctx, buyerInfo.UserID, buyerInfo.Address.BuyerAddressID)
	}
	if err != nil {
		return entity.GeoLocation{}, err
	}

	return buyerAddressGeo, nil
}

func (c *Cb3pfShippingOrderProcessor) sourcePrioritizeFuncWithSellerTag(
	ctx context.Context,
	is3PFHybrid bool,
	sourceToShopWarehouse map[string]entity.ShopWarehouse,
) ItemOrganizeSourcePrioritizeFunc {
	return func(sources []string) ([]string, error) {
		if is3PFHybrid {
			// Local warehouse has higher priority
			sort.SliceStable(sources, func(i, j int) bool {
				if sourceToShopWarehouse[sources[i]].IsLocalWarehouse(ctx) && !sourceToShopWarehouse[sources[j]].IsLocalWarehouse(ctx) {
					return true
				}
				if !sourceToShopWarehouse[sources[i]].IsLocalWarehouse(ctx) && sourceToShopWarehouse[sources[j]].IsLocalWarehouse(ctx) {
					return false
				}
				return sources[i] < sources[j]
			})
			return sources, nil
		}

		sort.SliceStable(sources, func(i, j int) bool {
			return sources[i] < sources[j]
		})
		return sources, nil
	}
}

func (c *Cb3pfShippingOrderProcessor) generateMetrics(ctx context.Context, is3PFHybrid bool, count int) {
	if is3PFHybrid {
		metrics.GenerateIGSOrderMetrics(ctx, constant.IGSOrderType3PFHybrid, count)
	} else {
		metrics.GenerateIGSOrderMetrics(ctx, constant.IGSOrderType3PFOnly, count)
	}
}
