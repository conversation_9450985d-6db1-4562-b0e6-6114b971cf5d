package item_grouping

import "context"

type ShippingOrderProcessor interface {
	Name() ShippingOrderProcessorName
	IsAbleToProcess(ctx context.Context, shippingOrder ShippingOrder) bool
	DoProcess(ctx context.Context, buyerInfo BuyerInfo, shippingOrders []ShippingOrder) ([]ShippingOrder, error)
}

type ShippingOrderProcessorName string

const (
	AdvanceBookingShippingOrderProcessorName ShippingOrderProcessorName = "AdvanceBooking"
	CBLFFShippingOrderProcessorName          ShippingOrderProcessorName = "CBLFF"
	PffShippingOrderProcessorName            ShippingOrderProcessorName = "PFF"
	CB3PFShippingOrderProcessorName          ShippingOrderProcessorName = "CB3PF"
)
