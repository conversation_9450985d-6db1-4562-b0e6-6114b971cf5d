package item_grouping

import (
	"context"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

type ItemGrouper interface {
	GroupItems(ctx context.Context, req *entity.ItemGroupRequest) (*entity.ItemGroupResult, fsserr.Error)
}

type DefaultItemGrouper struct {
	configAccessor config.ConfAccessor

	advanceBookingShippingOrderProcessor *AdvanceBookingShippingOrderProcessor
	cbLFFShippingOrderProcessor          *CBLFFShippingOrderProcessor
	pffShippingOrderProcessor            *PffShippingOrderProcessor
	cb3pfShippingOrderProcess            *Cb3pfShippingOrderProcessor
	shopeeShippingOrderProcessor         *ShopeeShippingOrderProcessor
}

func NewItemGrouper(
	confAccessor config.ConfAccessor,
	advanceBookingShippingOrderProcessor *AdvanceBookingShippingOrderProcessor,
	cbLFFShippingOrderProcessor *CBLFFShippingOrderProcessor,
	pffShippingOrderProcessor *PffShippingOrderProcessor,
	cb3pfShippingOrderProcess *Cb3pfShippingOrderProcessor,
	shopeeShippingOrderProcessor *ShopeeShippingOrderProcessor,
) *DefaultItemGrouper {
	return &DefaultItemGrouper{
		configAccessor:                       confAccessor,
		advanceBookingShippingOrderProcessor: advanceBookingShippingOrderProcessor,
		cbLFFShippingOrderProcessor:          cbLFFShippingOrderProcessor,
		pffShippingOrderProcessor:            pffShippingOrderProcessor,
		cb3pfShippingOrderProcess:            cb3pfShippingOrderProcess,
		shopeeShippingOrderProcessor:         shopeeShippingOrderProcessor,
	}
}

func (g *DefaultItemGrouper) GroupItems(ctx context.Context, req *entity.ItemGroupRequest) (*entity.ItemGroupResult, fsserr.Error) {

	return &entity.ItemGroupResult{}, nil
}
