package item_grouping

import (
	"context"
	"sort"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/business_config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/metrics"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/collection"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/typ"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/abtesting"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/address"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/order"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/warehouse_priority"
	Logger "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/logger"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/shared"
)

type PffShippingOrderProcessor struct {
	confAccessor            config.ConfAccessor
	addressService          address.AddrService
	whsPriorityService      warehouse_priority.WarehousePriorityService
	abtestService           abtesting.Service
	itemOrganizer           ItemOrganizer
	salesOrdersCountService order.SalesOrdersCountService
}

func NewPffShippingOrderProcessor(
	confAccessor config.ConfAccessor,
	addressService address.AddrService,
	whsPriorityService warehouse_priority.WarehousePriorityService,
	abTestService abtesting.Service,
	itemOrganizer ItemOrganizer,
	salesOrdersCountService order.SalesOrdersCountService) *PffShippingOrderProcessor {
	return &PffShippingOrderProcessor{
		confAccessor:            confAccessor,
		addressService:          addressService,
		whsPriorityService:      whsPriorityService,
		abtestService:           abTestService,
		itemOrganizer:           itemOrganizer,
		salesOrdersCountService: salesOrdersCountService,
	}
}

func (p *PffShippingOrderProcessor) Name() ShippingOrderProcessorName {
	return PffShippingOrderProcessorName
}

func (p *PffShippingOrderProcessor) IsAbleToProcess(ctx context.Context, shippingOrder ShippingOrder) bool {
	return !shippingOrder.IsSourceSelected() && !shippingOrder.FromMultipleShop() &&
		shippingOrder.ShopInfo().ShopeeWarehouseFulfilmentCapability && shippingOrder.ShopInfo().SellerStockFulfilmentCapability
}

func (p *PffShippingOrderProcessor) DoProcess(ctx context.Context, buyerInfo BuyerInfo, shippingOrders []ShippingOrder) ([]ShippingOrder, error) {
	shopIDSet := collection.NewSet[uint64]()
	for _, shippingOrder := range shippingOrders {
		shopIDSet.Add(shippingOrder.ShopInfo().ShopID)
	}
	shopIDToStrategy := p.whsPriorityService.BatchGetItemOrganizeStrategy(
		ctx,
		shopIDSet.Values(),
		shared.ItemOrganizeStrategyMinimumParcel,
	)

	var processedOrders []ShippingOrder
	for _, shippingOrder := range shippingOrders {
		splitOrders, err := p.splitSingleShippingOrder(ctx, buyerInfo, shippingOrder, shopIDToStrategy[shippingOrder.ShopInfo().ShopID])
		if err != nil {
			return nil, err
		}

		processedOrders = append(processedOrders, splitOrders...)
	}
	return processedOrders, nil
}

func (p *PffShippingOrderProcessor) splitSingleShippingOrder(
	ctx context.Context,
	buyerInfo BuyerInfo,
	shippingOrder ShippingOrder,
	strategy shared.ItemOrganizeStrategy,
) ([]ShippingOrder, error) {
	if p.shouldUseSellerMultiWHPFFFlow(ctx, buyerInfo.UserID) {
		return p.splitSingleSellerMultiWHPFFShippingOrder(ctx, buyerInfo, shippingOrder, shared.ItemOrganizeStrategyMinimumParcel)
	} else {
		if shippingOrder.ShopInfo().PFFCheckoutImprovementWhitelist {
			return p.splitSingleImprovedPFFShippingOrder(ctx, buyerInfo, shippingOrder, strategy)
		}
		return p.splitSingleNormalPFFShippingOrder(ctx, buyerInfo, shippingOrder, strategy)
	}
}

func (p *PffShippingOrderProcessor) splitSingleSellerMultiWHPFFShippingOrder(
	ctx context.Context,
	buyerInfo BuyerInfo,
	shippingOrder ShippingOrder,
	strategy shared.ItemOrganizeStrategy,
) ([]ShippingOrder, error) {
	keyToItems, err := p.itemOrganizer.OrganizeItems(
		ctx,
		shippingOrder.Items,
		p.pffSourceSelectForSellerMultiWH,
		p.pffSourcePrioritize(ctx, shippingOrder.ShopInfo(), buyerInfo.Address),
		strategy,
	)
	if err != nil {
		return nil, err
	}

	sourceToAddressID := make(map[string]uint64)
	for key := range keyToItems {
		if key.FulfilmentType == shared.FulfilmentTypeShopee {
			warehouseAddress, err := p.addressService.GetShopeeWarehouseAddress(ctx, key.FulfilmentSource)
			if err != nil {
				return nil, err
			}
			sourceToAddressID[key.FulfilmentSource] = warehouseAddress.AddressID
		}
	}
	for _, warehouse := range shippingOrder.ShopInfo().Warehouses {
		sourceToAddressID[warehouse.LocationID] = warehouse.AddressID
	}

	splitOrders := splitShippingOrderByOrganizedItems(shippingOrder, keyToItems, sourceToAddressID)
	metrics.GenerateIGSOrderMetrics(ctx, constant.IGSOrderTypePFFSellerMultiWH, len(splitOrders))
	return splitOrders, nil
}

func (p *PffShippingOrderProcessor) splitSingleImprovedPFFShippingOrder(
	ctx context.Context,
	buyerInfo BuyerInfo,
	shippingOrder ShippingOrder,
	strategy shared.ItemOrganizeStrategy,
) ([]ShippingOrder, error) {
	keyToItems, err := p.itemOrganizer.OrganizeItems(
		ctx,
		shippingOrder.Items,
		p.pffSourceSelect,
		p.pffSourcePrioritize(ctx, shippingOrder.ShopInfo(), buyerInfo.Address),
		strategy,
	)
	if err != nil {
		return nil, err
	}

	sourceToAddressID := make(map[string]uint64)
	for key := range keyToItems {
		if key.FulfilmentType == shared.FulfilmentTypeShopee {
			warehouseAddress, err := p.addressService.GetShopeeWarehouseAddress(ctx, key.FulfilmentSource)
			if err != nil {
				return nil, err
			}
			sourceToAddressID[key.FulfilmentSource] = warehouseAddress.AddressID
		}
	}

	return splitShippingOrderByOrganizedItems(shippingOrder, keyToItems, sourceToAddressID), nil
}

func (p *PffShippingOrderProcessor) splitSingleNormalPFFShippingOrder(
	ctx context.Context,
	buyerInfo BuyerInfo,
	shippingOrder ShippingOrder,
	strategy shared.ItemOrganizeStrategy,
) ([]ShippingOrder, error) {
	shopeeItems, sellerItems, err := p.splitItemsByFulfilmentType(ctx, shippingOrder, buyerInfo.Address)
	if err != nil {
		return nil, err
	}

	var splitOrders []ShippingOrder
	if len(shopeeItems) > 0 {
		keyToItems, err := p.itemOrganizer.OrganizeItems(
			ctx,
			shopeeItems,
			p.shopeeSourceSelect,
			p.shopeeSourcePrioritize(ctx, buyerInfo.Address),
			strategy,
		)
		if err != nil {
			return nil, err
		}

		sourceToAddressID := make(map[string]uint64)
		for key := range keyToItems {
			warehouseAddress, err := p.addressService.GetShopeeWarehouseAddress(ctx, key.FulfilmentSource)
			if err != nil {
				return nil, err
			}
			sourceToAddressID[key.FulfilmentSource] = warehouseAddress.AddressID
		}

		shopeeOrders := splitShippingOrderByOrganizedItems(shippingOrder, keyToItems, sourceToAddressID)
		metrics.GenerateIGSOrderMetrics(ctx, constant.IGSOrderTypePFFShopee, len(shopeeOrders))
		splitOrders = append(splitOrders, shopeeOrders...)
	}
	if len(sellerItems) > 0 {
		keyToItems, err := p.itemOrganizer.OrganizeItems(
			ctx,
			sellerItems,
			p.sellerSourceSelect,
			p.sellerSourcePrioritize,
			strategy,
		)
		if err != nil {
			return nil, err
		}

		sellerOrders := splitShippingOrderByOrganizedItems(shippingOrder, keyToItems, nil)
		metrics.GenerateIGSOrderMetrics(ctx, constant.IGSOrderTypePFFSeller, len(sellerOrders))
		splitOrders = append(splitOrders, sellerOrders...)
	}

	return splitOrders, nil
}

func (p *PffShippingOrderProcessor) splitItemsByFulfilmentType(
	ctx context.Context,
	shippingOrder ShippingOrder,
	buyerAddress entity.ItemGroupBuyerAddress,
) ([]ShippingOrderItem, []ShippingOrderItem, error) {
	var splitter ItemSplitter
	if p.isPrioritizeSellerWarehouse(ctx, shippingOrder, buyerAddress) {
		splitter = newFulfilmentTypeItemSplitter([]shared.FulfilmentType{shared.FulfilmentTypeSeller, shared.FulfilmentTypeShopee}, nil)
	} else {
		splitter = newFulfilmentTypeItemSplitter([]shared.FulfilmentType{shared.FulfilmentTypeShopee, shared.FulfilmentTypeSeller}, nil)
	}

	fulfilmentTypeToItems, err := splitter.SplitItems(shippingOrder.Items)
	if err != nil {
		return nil, nil, err
	}

	return fulfilmentTypeToItems[shared.FulfilmentTypeShopee], fulfilmentTypeToItems[shared.FulfilmentTypeSeller], nil
}

func (p *PffShippingOrderProcessor) pffSourceSelectForSellerMultiWH(item ShippingOrderItem) (StockLocations, error) {
	locations := item.ShopeeStocks()
	locations = append(locations, item.SellerStocks()...)
	return locations, nil
}

func (p *PffShippingOrderProcessor) pffSourceSelect(item ShippingOrderItem) (StockLocations, error) {
	locations := item.ShopeeStocks()
	if len(item.SellerStocks()) > 1 {
		locations = append(locations, item.SellerStocks()[:1]...)
	} else {
		locations = append(locations, item.SellerStocks()...)
	}
	return locations, nil
}

func (p *PffShippingOrderProcessor) pffSourcePrioritize(
	ctx context.Context,
	shopInfo ShopInfo,
	buyerAddress entity.ItemGroupBuyerAddress,
) ItemOrganizeSourcePrioritizeFunc {
	return func(sources []string) ([]string, error) {
		return p.whsPriorityService.PrioritizeShopeeAndSellerWarehouse(ctx, shopInfo.ShopID, buyerAddress, sources)
	}
}

func (p *PffShippingOrderProcessor) shopeeSourceSelect(item ShippingOrderItem) (StockLocations, error) {
	return item.ShopeeStocks(), nil
}

func (p *PffShippingOrderProcessor) shopeeSourcePrioritize(
	ctx context.Context,
	buyerAddress entity.ItemGroupBuyerAddress,
) ItemOrganizeSourcePrioritizeFunc {
	return func(sources []string) ([]string, error) {
		return p.whsPriorityService.PrioritizeShopeeWarehouse(ctx, buyerAddress, sources)
	}
}

func (p *PffShippingOrderProcessor) sellerSourceSelect(item ShippingOrderItem) (StockLocations, error) {
	if len(item.SellerStocks()) > 1 {
		return item.SellerStocks()[:1], nil
	}
	return item.SellerStocks(), nil
}

func (p *PffShippingOrderProcessor) sellerSourcePrioritize(sources []string) ([]string, error) {
	sort.SliceStable(sources, func(i, j int) bool {
		return sources[i] < sources[j]
	})
	return sources, nil
}

func (p *PffShippingOrderProcessor) shouldUseSellerMultiWHPFFFlow(ctx context.Context, buyerID typ.UserIdType) bool {
	if !p.confAccessor.GetSupportSellerMultiWhPFF(ctx) {
		return false
	}

	sellerMultiWHWithPartialFBSConfig := p.confAccessor.GetSellerMultiWHWithPartialFBSConfig(ctx)
	if sellerMultiWHWithPartialFBSConfig.SkipCheckABTest {
		return true
	}

	result, err := p.abtestService.CheckUserInExperimentGroupUsingFeature(ctx, buyerID, sellerMultiWHWithPartialFBSConfig.ABTestConfig)
	if err != nil {
		Logger.CtxLogErrorf(ctx, "CheckUserInExperimentGroupUsingFeature error, user_id: %d, cfg: %s", buyerID, Logger.JsonString(sellerMultiWHWithPartialFBSConfig))
		return false
	}

	return result
}

func (p *PffShippingOrderProcessor) isPrioritizeSellerWarehouse(ctx context.Context, shippingOrder ShippingOrder, buyerAddress entity.ItemGroupBuyerAddress) bool {
	if !p.confAccessor.GetEnableAllocateWarehouseBySalesOrders(ctx) {
		return false
	}
	group := p.getAllocateWarehouseBySalesOrdersGroup(ctx, shippingOrder.ShopInfo().PFFAllocateBySalesOrdersGroupTag)
	if group.SellerTag == "" {
		Logger.CtxLogErrorf(
			ctx, "allocate warehouse by sales orders group not found, shop_id: %d, group_tag: %s",
			shippingOrder.ShopInfo().ShopID, shippingOrder.ShopInfo().PFFAllocateBySalesOrdersGroupTag)
		return false
	}

	compare, err := p.whsPriorityService.CompareShopeeAndSellerWarehousePriorityByESF(
		ctx,
		shippingOrder.ShopInfo().ShopID,
		buyerAddress,
		p.getAllAllocatableWarehouses(shippingOrder),
	)
	if err != nil {
		Logger.CtxLogErrorf(ctx, "compare shopee and seller warehouse priority failed, err: %v", err)
		return false
	}
	if compare != 0 {
		return compare > 0
	}

	count, err := p.salesOrdersCountService.GetShopSalesOrdersCount(ctx, shippingOrder.ShopInfo().ShopID, group.CalculateFromPastDays)
	if err != nil {
		Logger.CtxLogErrorf(ctx, "get shop sales orders count failed, err: %v", err)
		return false
	}

	if group.SellerWarehouseSalesOrdersMax != 0 && count.SellerWarehouseOrdersCount >= group.SellerWarehouseSalesOrdersMax {
		return false
	}
	if group.SellerWarehouseSalesOrdersMin != 0 && count.SellerWarehouseOrdersCount < group.SellerWarehouseSalesOrdersMin {
		return true
	}
	if group.SellerWarehouseSalesOrderRatio == 0 {
		return false
	}

	var ratio float64
	if count.SellerWarehouseOrdersCount+count.ShopeeWarehouseOrdersCount != 0 {
		ratio = float64(count.SellerWarehouseOrdersCount) / float64(count.SellerWarehouseOrdersCount+count.ShopeeWarehouseOrdersCount)
	}

	return ratio < group.SellerWarehouseSalesOrderRatio
}

func (p *PffShippingOrderProcessor) getAllAllocatableWarehouses(shippingOrder ShippingOrder) []string {
	warehouseSet := collection.NewSet[string]()
	shippingOrder.ForEachAllItems(func(item ShippingOrderItem) {
		for _, location := range item.GetStockLocations(shared.FulfilmentTypeShopee, shared.FulfilmentTypeSeller) {
			if location.AvailableStock > 0 {
				warehouseSet.Add(location.Source)
			}
		}
	})
	return warehouseSet.Values()
}

func (p *PffShippingOrderProcessor) getAllocateWarehouseBySalesOrdersGroup(ctx context.Context, sellerTag string) business_config.AllocateWarehouseBySalesOrdersGroup {
	for _, group := range p.confAccessor.GetAllocateWarehouseBySalesOrderGroups(ctx) {
		if group.SellerTag == sellerTag {
			return group
		}
	}
	return business_config.AllocateWarehouseBySalesOrdersGroup{}
}
