package shop

import (
	"context"

	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/seller_seller_address_core.pb"
	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/shop_core.pb"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/external/spexlib"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

type ShopService interface {
	BatchGetShopDetail(ctx context.Context, shopIDs []uint64) (map[uint64]entity.ShopDetailInfo, fsserr.Error)
	BatchGetShopWarehouseFlag(ctx context.Context, shopIDs []uint64) (map[uint64]entity.ShopWarehouseFlag, fsserr.Error)
	BatchGetShopWarehouses(ctx context.Context, shopIDs []uint64) (map[uint64][]entity.ShopWarehouse, fsserr.Error)
}

type ShopServiceImpl struct {
	SpexClient spexlib.SpexClient
}

func NewShopService(
	spexClient spexlib.SpexClient,
) *ShopServiceImpl {
	return &ShopServiceImpl{
		SpexClient: spexClient,
	}
}

func (s ShopServiceImpl) BatchGetShopDetail(ctx context.Context, shopIDs []uint64) (map[uint64]entity.ShopDetailInfo, fsserr.Error) {
	if len(shopIDs) == 0 {
		return make(map[uint64]entity.ShopDetailInfo), nil
	}

	// 将 uint64 转换为 int64
	shopIDList := make([]int64, 0, len(shopIDs))
	for _, shopID := range shopIDs {
		shopIDList = append(shopIDList, int64(shopID))
	}

	// 构造请求
	req := &shop_core.GetShopBatchRequest{
		ShopidList: shopIDList,
	}

	// 调用 Spex API
	resp, err := s.SpexClient.BatchGetShop(ctx, req)
	if err != nil {
		return nil, fsserr.With(fsserr.SpexError, err)
	}

	// 解析响应并转换为 entity.ShopDetailInfo
	result := make(map[uint64]entity.ShopDetailInfo, len(shopIDs))
	for _, shop := range resp.GetShopList() {
		if shop == nil {
			continue
		}

		shopID := uint64(shop.GetShopid())
		shopDetailInfo := entity.ShopDetailInfo{
			ShopID:             shopID,
			UserID:             uint64(shop.GetUserid()),
			IsSIPPrimary:       shop.GetIsSipPrimary(),
			IsSIPAffiliated:    shop.GetIsSipAffiliated(),
			IsShipFromOverseas: shop.GetIsShipFromOverseas(),
		}

		result[shopID] = shopDetailInfo
	}

	return result, nil
}

func (s ShopServiceImpl) BatchGetShopWarehouseFlag(ctx context.Context, shopIDs []uint64) (map[uint64]entity.ShopWarehouseFlag, fsserr.Error) {
	if len(shopIDs) == 0 {
		return make(map[uint64]entity.ShopWarehouseFlag), nil
	}

	// 将 uint64 转换为 int64
	shopIDList := make([]int64, 0, len(shopIDs))
	for _, shopID := range shopIDs {
		shopIDList = append(shopIDList, int64(shopID))
	}

	// 调用 Spex API
	resp, err := s.SpexClient.BatchGetWarehouseFlagByShop(ctx, shopIDList)
	if err != nil {
		return nil, fsserr.With(fsserr.SpexError, err)
	}

	result := make(map[uint64]entity.ShopWarehouseFlag, len(shopIDs))
	for _, shopFlag := range resp.GetWarehouseFlagList() {
		if shopFlag == nil {
			continue
		}
		shopID := uint64(shopFlag.GetShopId())
		warehouseFlag := entity.ShopWarehouseFlag{
			WarehouseFlag:            int(shopFlag.GetWarehouseFlag()),
			CanUseWarehouseForPickup: shopFlag.GetCanUseWarehouseForPickup(),
			CanUseWarehouseForReturn: shopFlag.GetCanUseWarehouseForReturn(),
		}
		result[shopID] = warehouseFlag
	}

	return result, nil
}

func (s ShopServiceImpl) BatchGetShopWarehouses(ctx context.Context, shopIDs []uint64) (map[uint64][]entity.ShopWarehouse, fsserr.Error) {
	if len(shopIDs) == 0 {
		return make(map[uint64][]entity.ShopWarehouse), nil
	}

	// 将 uint64 转换为 int64
	shopIDList := make([]int64, 0, len(shopIDs))
	for _, shopID := range shopIDs {
		shopIDList = append(shopIDList, int64(shopID))
	}

	// 构造请求
	req := &seller_seller_address_core.BatchGetWarehouseByShopWithoutPaginationRequest{
		ShopIdList: shopIDList,
	}

	// 调用 Spex API
	resp, err := s.SpexClient.BatchGetWarehouseByShopWithoutPagination(ctx, req)
	if err != nil {
		return nil, fsserr.With(fsserr.SpexError, err)
	}

	result := make(map[uint64][]entity.ShopWarehouse, len(shopIDs))
	for _, shopWarehouse := range resp.GetWarehouseList() {
		shopID := uint64(shopWarehouse.GetShopId())
		result[shopID] = append(result[shopID], entity.ShopWarehouse{
			ShopID:     shopID,
			LocationID: shopWarehouse.GetWarehouse().GetLocationId(),
			AddressID:  uint64(shopWarehouse.GetWarehouse().GetAddressId()),
			Region:     shopWarehouse.GetWarehouse().GetRegion(),
		})
	}

	return result, nil
}
