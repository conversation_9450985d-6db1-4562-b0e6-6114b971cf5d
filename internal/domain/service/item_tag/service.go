package item_tag

import (
	"context"
)

type ItemTagService interface {
	BatchGetItemModelLabelIDs(ctx context.Context, itemModelIDs []uint64) (map[uint64][]uint64, error)
}

type ItemTagServiceImpl struct {
	api TagAPI `inject:"item_tag_api_cacheable"`
}

func NewItemTagService(api TagAPI) *ItemTagServiceImpl {
	return &ItemTagServiceImpl{api: api}
}

func (i *ItemTagServiceImpl) BatchGetItemModelLabelIDs(ctx context.Context, itemModelIDs []uint64) (map[uint64][]uint64, error) {
	modelIDToModelLabel, err := i.api.BatchGetItemModelIDToLabels(ctx, itemModelIDs)
	if err != nil {
		return nil, err
	}

	modelIDToLabelIDs := make(map[uint64][]uint64)
	for _, modelLabel := range modelIDToModelLabel {
		modelIDToLabelIDs[modelLabel.GetModelId()] = modelLabel.GetLabelIdList()
	}
	return modelIDToLabelIDs, nil
}
