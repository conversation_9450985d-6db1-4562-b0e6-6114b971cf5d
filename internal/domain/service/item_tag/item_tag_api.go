package item_tag

import (
	"context"

	"git.garena.com/shopee/bg-logistics/logistics/spex-proto-center/spcli/gen/go/marketplace_listing_itemtagservice_querying_api.pb"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/collection"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/utils/common"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/entity"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/external/spexlib"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/pkg/utils/fsserr"
)

type TagAPI interface {
	GetItemLabels(ctx context.Context, itemTagIDs []entity.ItemTagID) (map[entity.ItemTagID]entity.ItemTag, error)
	BatchGetItemModelIDToLabels(ctx context.Context, itemModelIDs []uint64) (map[uint64]*marketplace_listing_itemtagservice_querying_api.ModelLabel, error)
}

type TagAPIImpl struct {
	SpexClient spexlib.SpexClient
}

func NewTagAPI(spexClient spexlib.SpexClient) *TagAPIImpl {
	return &TagAPIImpl{
		SpexClient: spexClient,
	}
}

func (t *TagAPIImpl) GetItemLabels(ctx context.Context, itemTagIDs []entity.ItemTagID) (map[entity.ItemTagID]entity.ItemTag, error) {
	itemIDSet, tagIDSet := collection.NewSet[uint64](), collection.NewSet[uint64]()
	for _, id := range itemTagIDs {
		itemIDSet.Add(id.ItemID)
		tagIDSet.Add(id.TagID)
	}

	itemIDs := itemIDSet.ToSlice()
	if len(itemIDs) == 0 {
		return nil, nil
	}

	caller := common.NewConcurrencySplitCaller[uint64, *marketplace_listing_itemtagservice_querying_api.ItemLabel]()
	itemLabels, err := caller.Call(ctx, itemIDs, int(marketplace_listing_itemtagservice_querying_api.GetItemLabelsRequest_MAX_ITEM_IDS_LEN),
		func(ctx context.Context, queries []uint64) ([]*marketplace_listing_itemtagservice_querying_api.ItemLabel, error) {
			req := &marketplace_listing_itemtagservice_querying_api.GetItemLabelsRequest{
				ItemIds: queries,
			}
			itemLabels, err := t.SpexClient.GetItemLabels(ctx, req)
			if err != nil {
				return nil, err
			}
			return itemLabels, nil
		})
	if err != nil {
		return nil, fsserr.With(fsserr.SpexError, err)
	}

	itemTagIDSet := map[entity.ItemTagID]struct{}{}
	for _, itemLabel := range itemLabels {
		for _, labelID := range itemLabel.GetLabelIds() {
			if tagIDSet.Contains(labelID) {
				itemTagIDSet[entity.ItemTagID{ItemID: itemLabel.GetItemId(), TagID: labelID}] = struct{}{}
			}
		}
	}

	resultMap := make(map[entity.ItemTagID]entity.ItemTag)
	for _, id := range itemTagIDs {
		_, appear := itemTagIDSet[id]
		resultMap[id] = entity.ItemTag{
			ItemTagID: id,
			IsTagged:  appear,
		}
	}

	return resultMap, nil
}

func (t *TagAPIImpl) BatchGetItemModelIDToLabels(ctx context.Context, itemModelIDs []uint64) (map[uint64]*marketplace_listing_itemtagservice_querying_api.ModelLabel, error) {
	caller := common.NewConcurrencySplitCaller[uint64, *marketplace_listing_itemtagservice_querying_api.ModelLabel]()
	modelLabels, err := caller.Call(ctx, itemModelIDs, 50, t.batchGetItemModelLabels)
	if err != nil {
		return nil, err
	}

	modelIDToLabelInfo := make(map[uint64]*marketplace_listing_itemtagservice_querying_api.ModelLabel)
	for _, modelLabel := range modelLabels {
		modelIDToLabelInfo[modelLabel.GetModelId()] = modelLabel
	}
	return modelIDToLabelInfo, nil
}

func (t *TagAPIImpl) batchGetItemModelLabels(ctx context.Context, itemModelIDs []uint64) ([]*marketplace_listing_itemtagservice_querying_api.ModelLabel, error) {
	request := &marketplace_listing_itemtagservice_querying_api.GetModelLabelsRequest{
		ModelIds: itemModelIDs,
	}
	modelLabels, err := t.SpexClient.GetModelLabels(ctx, request)
	if err != nil {
		return nil, err
	}
	return modelLabels, nil
}
