// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"context"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/application/service"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/bootstrap"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/application_config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/business_config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/mutable_application_config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/server_config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/abtesting"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/address"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_grouping"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/item_tag"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/order"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/seller_tag"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/shop"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/shop_local_sip"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/soc"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/domain/service/warehouse_priority"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/external/spexlib"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/redishelper"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/interface/handler"
)

// Injectors from injector.go:

func NewConfAccessor(ctx context.Context) config.ConfAccessor {
	applicationAccessorImpl := application_config.NewApplicationAccessorImpl()
	businessAccessorImpl := business_config.NewBusinessAccessorImpl()
	mutableApplicationAccessorImpl := mutable_application_config.NewMutableApplicationAccessorImpl()
	serverAccessorImpl := server_config.NewServerAccessorImpl()
	confAccessorImpl := config.NewConfAccessorImpl(applicationAccessorImpl, businessAccessorImpl, mutableApplicationAccessorImpl, serverAccessorImpl)
	return confAccessorImpl
}

func InitRestServer(ctx context.Context, confAccessor config.ConfAccessor) (*handler.RestServer, error) {
	socServiceImpl := soc.NewSOCService()
	warehousePriorityServiceImpl := warehouse_priority.NewWarehousePriorityService()
	spexClientImpl := spexlib.NewSpexClientImpl(confAccessor)
	addrServiceImpl := address.NewAddressService(spexClientImpl, confAccessor)
	advanceBookingShippingOrderProcessor := item_grouping.NewAdvanceBookingShippingOrderProcessor(socServiceImpl, warehousePriorityServiceImpl, addrServiceImpl)
	warehousePriorityStrategyItemOrganizer := item_grouping.NewWarehousePriorityStrategyItemOrganizer()
	minimumParcelStrategyItemOrganizer := item_grouping.NewMinimumParcelStrategyItemOrganizer(confAccessor, warehousePriorityStrategyItemOrganizer)
	itemOrganizerImpl := item_grouping.NewItemOrganizer(minimumParcelStrategyItemOrganizer, warehousePriorityStrategyItemOrganizer)
	shopLocalSIPServiceImpl := shop_local_sip.NewShopLocalSIPService(spexClientImpl, confAccessor)
	cblffShippingOrderProcessor := item_grouping.NewCBLFFShippingOrderProcessor(confAccessor, itemOrganizerImpl, warehousePriorityServiceImpl, addrServiceImpl, shopLocalSIPServiceImpl)
	serviceImpl := abtesting.NewServiceImpl(ctx, confAccessor)
	globalRedisClients, err := redishelper.InitRedisClientMap()
	if err != nil {
		return nil, err
	}
	cacheStoreImpl := order.NewCacheStore(globalRedisClients)
	sellerTagApiImpl, err := seller_tag.NewSellerTagApiImpl(spexClientImpl, confAccessor)
	if err != nil {
		return nil, err
	}
	sellerTagServiceImpl := seller_tag.NewSellerTagService(confAccessor, sellerTagApiImpl)
	salesOrdersCountServiceImpl := order.NewSalesOrdersCountServiceImpl(cacheStoreImpl, confAccessor, sellerTagServiceImpl)
	pffShippingOrderProcessor := item_grouping.NewPffShippingOrderProcessor(confAccessor, addrServiceImpl, warehousePriorityServiceImpl, serviceImpl, itemOrganizerImpl, salesOrdersCountServiceImpl)
	shopServiceImpl := shop.NewShopService(spexClientImpl)
	cb3pfShippingOrderProcessor := item_grouping.NewCb3pfShippingOrderProcessor(confAccessor, itemOrganizerImpl, shopServiceImpl, addrServiceImpl, warehousePriorityServiceImpl, shopLocalSIPServiceImpl)
	tagAPIImpl := item_tag.NewTagAPI(spexClientImpl)
	itemTagServiceImpl := item_tag.NewItemTagService(tagAPIImpl)
	shopeeShippingOrderProcessor := item_grouping.NewShopeeShippingOrderProcessor(confAccessor, itemOrganizerImpl, itemTagServiceImpl, warehousePriorityServiceImpl, addrServiceImpl)
	defaultItemGrouper := item_grouping.NewItemGrouper(confAccessor, advanceBookingShippingOrderProcessor, cblffShippingOrderProcessor, pffShippingOrderProcessor, cb3pfShippingOrderProcessor, shopeeShippingOrderProcessor)
	itemGroupingServiceImpl := service.NewItemGroupingService(defaultItemGrouper)
	itemGroupingHandler := handler.NewItemGroupingHandler(itemGroupingServiceImpl)
	restServer := &handler.RestServer{
		ItemGroupingHandler: itemGroupingHandler,
	}
	return restServer, nil
}

// injector.go:

func InitConf(ctx context.Context, confAccessor config.ConfAccessor) error {
	if err := bootstrap.InitChassisConfigListener(ctx); err != nil {
		return err
	}
	if err := confAccessor.Init(ctx); err != nil {
		return err
	}
	return nil
}
