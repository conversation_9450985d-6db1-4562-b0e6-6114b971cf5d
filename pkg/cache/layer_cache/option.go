package layer_cache

import (
	"context"
)

const (
	Second              = 1
	Minute              = 60
	hotLimitExpiration  = 2 * Minute  // 热数据，最多2分钟过期
	warmLimitExpiration = 5 * Minute  // 较热数据，最多5分钟过期
	coldLimitExpiration = 60 * Minute // 较冷数据，最多60分钟过期
)

type (
	LevelOptions struct {
		loader        DataLoader
		unmarshalFunc UnmarshalFunc
		expiration    uint32
	}

	DataLoader    func(ctx context.Context, key string) (interface{}, error)
	UnmarshalFunc func([]byte) (interface{}, error)

	LevelOption func(o *LevelOptions)
)

func WithCustomExpire(expSecs uint32) LevelOption {
	return func(o *LevelOptions) {
		o.expiration = expSecs
	}
}

func WithHotExpire() LevelOption {
	return func(o *LevelOptions) {
		o.expiration = hotLimitExpiration
	}
}

func WithWarmExpire() LevelOption {
	return func(o *LevelOptions) {
		o.expiration = warmLimitExpiration
	}
}

func WithColdExpire() LevelOption {
	return func(o *LevelOptions) {
		o.expiration = coldLimitExpiration
	}
}

func WithLoader(loader DataLoader) LevelOption {
	return func(o *LevelOptions) {
		o.loader = loader
	}
}

func WithUnmarshalFunc(unmarshalFunc UnmarshalFunc) LevelOption {
	return func(o *LevelOptions) {
		o.unmarshalFunc = unmarshalFunc
	}
}
