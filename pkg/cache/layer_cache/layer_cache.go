package layer_cache

import (
	"context"
	"errors"
	"strings"
	"sync"
	"time"

	"git.garena.com/shopee/bg-logistics/go/go-redis"
	"git.garena.com/shopee/bg-logistics/logistics/layered-cache/cache"
	"git.garena.com/shopee/bg-logistics/logistics/layered-cache/cache/api"
	"git.garena.com/shopee/bg-logistics/logistics/layered-cache/cache/client/layered"
	"git.garena.com/shopee/bg-logistics/logistics/layered-cache/cache/client/memory"
	"git.garena.com/shopee/bg-logistics/logistics/layered-cache/cache/client/remote"
	"github.com/modern-go/reflect2"
	"github.com/zeromicro/go-zero/core/syncx"

	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/config/mutable_application_config"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/constant"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/common/monitorutils"
	"git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/infrastructure/redishelper"
	Logger "git.garena.com/shopee/bg-logistics/logistics/fulfillment-sourcing-service/internal/logger"
)

var (
	dataLoaderMiss         = errors.New("data loader not found")
	dataNotFound           = errors.New("data not found")
	namespaceNotFound      = errors.New("namespace not found")
	namespaceGroupNotFound = errors.New("namespace group not found")
	unmarshalFuncNotFound  = errors.New("unmarshal func not found")
	singleFlight           = syncx.NewSingleFlight()
)

var (
	newLayerCacheManager *LayerCacheManager
	initOnce             sync.Once // 保证 layerCache 只获取一次
)

func IsDataNotFoundErr(err error) bool {
	return err == dataNotFound || err == cache.HotPenetrationError
}

type LayerCacheManager struct {
	caches map[NamespaceGroup]*LayerCache
}

func NewLayerCacheManager(ctx context.Context, confAccessor config.ConfAccessor, globalRedisClients redishelper.GlobalRedisClients) *LayerCacheManager {
	initOnce.Do(func() {
		cfg := confAccessor.GetLayerCacheConfig(ctx)
		namespaceGroupCfg := mutable_application_config.LayerCacheNamespaceConfig{
			MemSize:          cfg.MemSize,
			MemNumOfCounters: cfg.MemNumOfCounters,
			MemLogicTTL:      cfg.MemLogicTTL,
			MemPhysicalTTL:   cfg.MemPhysicalTTL,
		}
		layerCacheManager := &LayerCacheManager{
			caches: make(map[NamespaceGroup]*LayerCache),
		}
		for _, namespaceGroup := range namespaceMap {
			if _, ok := layerCacheManager.getCache(namespaceGroup); ok {
				continue
			}
			client, redisErr := globalRedisClients.GetRedisClusterByClusterName(namespaceGroupRedisMap[namespaceGroup])
			if redisErr != nil {
				Logger.CtxLogErrorf(context.Background(), "init opt layer cache redis failed|namespace_group=%v|err=%v", namespaceGroup, redisErr)
				continue
			}

			namespaceConfig := namespaceGroupCfg
			namespaceCfg, nok := cfg.Namespaces[string(namespaceGroup)]
			if nok {
				namespaceConfig = updateMemoryCacheConfig(namespaceConfig, namespaceCfg)
			}
			memoryCacheConfig := memory.MemoryCacheConfig{
				Size:               uint32(namespaceConfig.MemSize),
				NumOfCounters:      uint32(namespaceConfig.MemNumOfCounters),
				PhysicalTtlSeconds: uint32(namespaceConfig.MemPhysicalTTL),
				LogicTtlSeconds:    uint32(namespaceConfig.MemLogicTTL),
			}
			if memoryCacheConfig.Size == 0 {
				memoryCacheConfig.Size = defaultMemorySize
			}
			c := layered.NewCacheClient(&layered.CacheConfig{
				MemoryCacheConfig: memoryCacheConfig,
				RedisConfig: remote.RedisConfig{
					Client: client,
				},
				LimitKeySize:         false,
				CircuitBreakerEnable: false,
				PenetrationQpsLimit:  uint32(cfg.PenetrationQpsLimit),
			})
			layerCache := &LayerCache{
				cache:        c,
				confAccessor: confAccessor,
				redisClient:  client,
			}
			layerCacheManager.setCache(namespaceGroup, layerCache)
		}
		newLayerCacheManager = layerCacheManager
	})
	return newLayerCacheManager
}

func (m *LayerCacheManager) Get(ctx context.Context, namespace Namespace, id string, opts ...LevelOption) (interface{}, error) {
	layerCache, err := m.getCacheByNamespace(ctx, namespace)
	if err != nil {
		return nil, err
	}
	return layerCache.Get(ctx, namespace, id, opts...)
}

func (m *LayerCacheManager) Set(ctx context.Context, namespace Namespace, id string, obj interface{}, expiration uint32, opts ...LevelOption) error {
	layerCache, err := m.getCacheByNamespace(ctx, namespace)
	if err != nil {
		return err
	}
	return layerCache.Set(ctx, namespace, id, obj, expiration, opts...)
}

func (m *LayerCacheManager) getCacheByNamespace(ctx context.Context, namespace Namespace) (*LayerCache, error) {
	namespaceGroup, ok := namespaceMap[namespace]
	if !ok {
		sendMonitor(ctx, namespace, "", constant.StatusNamespaceNotFound)
		return nil, namespaceGroupNotFound
	}
	layerCache, ok := m.getCache(namespaceGroup)
	if !ok {
		sendMonitor(ctx, namespace, "", constant.StatusNamespaceGroupNotFound)
		return nil, namespaceNotFound
	}
	return layerCache, nil
}

func (m *LayerCacheManager) getCache(group NamespaceGroup) (*LayerCache, bool) {
	if m == nil || m.caches == nil {
		return nil, false
	}
	layerCache, ok := m.caches[group]
	return layerCache, ok
}

func (m *LayerCacheManager) setCache(group NamespaceGroup, layerCache *LayerCache) {
	if m == nil || m.caches == nil {
		return
	}
	m.caches[group] = layerCache
}

type LayerCache struct {
	cache        api.CacheClient
	memoryCache  layered.CacheClient
	confAccessor config.ConfAccessor
	redisClient  *redis.Client
}

func (p *LayerCache) Get(ctx context.Context, namespace Namespace, id string, opts ...LevelOption) (interface{}, error) {
	return p.doGet(ctx, namespace, id, opts...)
}

func (p *LayerCache) doGet(ctx context.Context, namespace Namespace, id string, opts ...LevelOption) (interface{}, error) {
	layerCacheConfig := p.confAccessor.GetLayerCacheConfig(ctx)
	// get the default expireSeconds
	cacheOption := &LevelOptions{
		expiration: uint32(layerCacheConfig.MemPhysicalTTL),
	}
	timeout := defaultTimeout
	if layerCacheExpireConfig, layerCacheExpireConfigOk := layerCacheConfig.ExpireConfig[string(namespace)]; layerCacheExpireConfigOk {
		cacheOption.expiration = layerCacheExpireConfig.ExpireSeconds
		if layerCacheExpireConfig.Timeout > 0 {
			timeout = time.Duration(layerCacheExpireConfig.Timeout) * time.Millisecond
		}
	}
	ctx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	if len(opts) > 0 {
		for _, opt := range opts {
			opt(cacheOption)
		}
	}

	key := unique(namespace, id)
	// get from cache with unmarshal function
	if cacheOption.unmarshalFunc == nil {
		sendMonitor(ctx, namespace, id, constant.StatusUnmarshallFuncNotFound)
		return nil, unmarshalFuncNotFound
	}
	var data interface{}
	var cacheErr error
	data, cacheErr = p.cache.Get(ctx, key).Interface(func(bytes []byte) (interface{}, error) {
		return cacheOption.unmarshalFunc(bytes)
	})
	if cacheErr == nil {
		sendMonitor(ctx, namespace, id, constant.StatusSuccess)
		return data, nil
	}

	if cacheOption.loader == nil {
		sendMonitor(ctx, namespace, id, constant.StatusLoaderEmpty)
		return nil, dataLoaderMiss
	}

	//如果缓存没查到，去数据源查询（还有空值、熔断、限流等情况）
	if cacheErr != cache.Miss {
		//抛出一个错误给上层，和约定写法保持一致
		if cacheErr == cache.HotPenetrationError {
			sendMonitor(ctx, namespace, id, constant.StatusHotKey)
		} else {
			sendMonitor(ctx, namespace, id, constant.StatusError)
		}
		return nil, cacheErr
	}

	// 使用单飞模式降低并发请求
	var fErr error
	data, fErr = singleFlight.Do(unique(namespace, id), func() (interface{}, error) {
		singleData, err := cacheOption.loader(ctx, id)
		return singleData, err
	})
	if !reflect2.IsNil(fErr) {
		sendMonitor(ctx, namespace+"_loadErr", id+fErr.Error(), constant.StatusSuccess)
		return nil, fErr
	}
	if data == nil {
		sendMonitor(ctx, namespace+"_loadNil", id, constant.StatusSuccess)
		return nil, dataNotFound
	}

	sendMonitor(ctx, namespace, id, constant.StatusMiss)
	_ = p.cache.Set(ctx, key, data, cacheOption.expiration)
	return data, nil
}

func (p *LayerCache) Set(ctx context.Context, namespace Namespace, id string, obj interface{}, expiration uint32, opts ...LevelOption) error {
	cacheOption := &LevelOptions{}
	if len(opts) > 0 {
		for _, opt := range opts {
			opt(cacheOption)
		}
	}

	key := unique(namespace, id)
	return p.cache.Set(ctx, key, obj, expiration)
}

func sendMonitor(ctx context.Context, namespace Namespace, msg, status string) {
	_ = monitorutils.BusinessAwesomeReportEvent(ctx, constant.CatModuleLayeredCache, string(namespace), status, msg)
}

func unique(namespace Namespace, id string) string {
	var builder strings.Builder
	builder.Grow(len(namespace) + len(id) + 1)
	builder.WriteString(string(namespace))
	builder.WriteString(".")
	builder.WriteString(id)
	return builder.String()
}

func updateMemoryCacheConfig(source, target mutable_application_config.LayerCacheNamespaceConfig) mutable_application_config.LayerCacheNamespaceConfig {
	if target.MemSize != 0 {
		source.MemSize = target.MemSize
	}
	if target.MemNumOfCounters != 0 {
		source.MemNumOfCounters = target.MemNumOfCounters
	}
	if target.MemPhysicalTTL != 0 {
		source.MemPhysicalTTL = target.MemPhysicalTTL
	}
	if target.MemLogicTTL != 0 {
		source.MemLogicTTL = target.MemLogicTTL
	}
	return source
}
